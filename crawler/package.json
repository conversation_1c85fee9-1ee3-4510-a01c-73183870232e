{"name": "crawler", "version": "0.0.1", "type": "module", "description": "Crawlee project.", "dependencies": {"@aws-sdk/client-s3": "^3.824.0", "@crawlee/utils": "^3.13.6", "@keyv/redis": "^4.4.0", "camoufox-js": "^0.3.6", "crawlee": "^3.0.0", "dotenv": "^16.5.0", "keyv": "^5.3.4", "playwright": "*", "user-agents": "^1.1.561"}, "devDependencies": {"@apify/tsconfig": "^0.1.0", "@types/node": "^22.0.0", "@types/user-agents": "^1.0.4", "tsx": "^4.4.0", "typescript": "~5.8.0"}, "scripts": {"start": "npm run start:dev", "start:prod": "node dist/main.js", "start:dev": "tsx src/main.ts", "build": "tsc", "test": "echo \"Error: oops, the actor has no tests yet, sad!\" && exit 1", "postinstall": "npx crawlee install-playwright-browsers"}, "author": "It's not you it's me", "license": "ISC"}