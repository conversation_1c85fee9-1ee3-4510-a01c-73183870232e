FROM node:18-bullseye-slim

RUN apt-get update && \
    apt-get install -y \
      wget ca-certificates fonts-liberation \
      libatk1.0-0 libatk-bridge2.0-0 libcups2 libgtk-3-0 \
      libx11-xcb1 libxcomposite1 libxdamage1 libxrandr2 libgbm1 \
      libasound2 libpangocairo-1.0-0 libpangoft2-1.0-0 \
      libnspr4 libnss3 && \
    rm -rf /var/lib/apt/lists/*

WORKDIR /usr/src/app

COPY package.json pnpm-lock.yaml ./

RUN corepack enable && corepack prepare pnpm@latest --activate && \
    pnpm install --frozen-lockfile

RUN npx playwright install --with-deps

COPY . .

ENV NODE_ENV=production

CMD ["pnpm", "start"]
