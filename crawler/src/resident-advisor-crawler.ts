import 'dotenv/config'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, RequestQueue } from 'crawlee'
import { Sitemap } from '@crawlee/utils'
import { S3Client, PutObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3'
import { URL } from 'url'

const requestQueue = await RequestQueue.open()

const sitemapUrls = ['https://ra.co/sitemap.xml']
const sitemap = await Sitemap.load(sitemapUrls)
console.log(`Loaded ${sitemap.urls.length} URLs from RA sitemap`)

const eventUrls = sitemap.urls.filter((url) => url.includes('/events/'))
console.log(`Filtered ${eventUrls.length} event URLs`)

const BATCH_SIZE = Number(process.env.RA_BATCH_SIZE) || 200
console.log(`Queueing RA event URLs in batches of ${BATCH_SIZE}`)
for (let i = 0; i < eventUrls.length; i += BATCH_SIZE) {
  const batch = eventUrls.slice(i, i + BATCH_SIZE)
  console.log(`Queueing RA batch ${Math.floor(i / BATCH_SIZE) + 1}: ${batch.length} URLs`)
  await requestQueue.addRequests(
    batch.map((url) => ({ url, userData: { label: 'detail' } })),
  )
}

const {
  S3_ENDPOINT,
  S3_REGION,
  S3_ACCESS_KEY_ID,
  S3_SECRET_ACCESS_KEY,
} = process.env
if (!S3_ENDPOINT) {
  throw new Error('S3 configuration environment variables are not fully defined')
}
const s3Config: S3ClientConfig = {
  endpoint: S3_ENDPOINT,
  region: S3_REGION,
  credentials: { 
    accessKeyId: S3_ACCESS_KEY_ID || '',
    secretAccessKey: S3_SECRET_ACCESS_KEY || ''
  },
  forcePathStyle: true,
}
const s3 = new S3Client(s3Config)

export const residentAdvisorCrawler = new PlaywrightCrawler({
  requestQueue,
  browserPoolOptions: { useFingerprints: false },
  retryOnBlocked: true,
  minConcurrency: 5,
  maxConcurrency: 100,
  maxRequestsPerMinute: 60,
  maxRequestRetries: 100,
  navigationTimeoutSecs: 60,
  requestHandlerTimeoutSecs: 120,
  requestHandler: async ({ request, page, log }) => {
    const html = await page.content()
    const parsed = new URL(request.loadedUrl)
    let keyPath = `${parsed.hostname}${parsed.pathname}${parsed.search}`
    keyPath = keyPath.replace(/^\/+/, '').replace(/[\?\/&=]/g, '_')
    const s3Key = `${keyPath}.html`
    try {
      await s3.send(
        new PutObjectCommand({
          Bucket: 'resident-advisor',
          Key: s3Key,
          Body: html,
          ContentType: 'text/html',
        }),
      )
      log.info(`Uploaded HTML to s3://resident-advisor/${s3Key}`)
    } catch (err) {
      log.error(`Failed uploading to S3 for ${request.loadedUrl}: ${err}`)
    }
  },
  postNavigationHooks: [async ({ handleCloudflareChallenge }) => {
    await handleCloudflareChallenge()
  }],
})
