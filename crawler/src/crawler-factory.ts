import 'dotenv/config'
import { PlaywrightCrawler, RequestQueue, Configuration, KeyValueStore } from 'crawlee'
import { Sitemap } from '@crawlee/utils'
import { S3Client, PutObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3'
import { URL } from 'url'



interface CreateCrawlerOptions {
  sitemapUrls: string[]
  eventPathIncludes: string[]
  bucket: string
  batchSizeEnv?: string
  defaultBatchSize?: number
}

export async function createCrawler(options: CreateCrawlerOptions): Promise<PlaywrightCrawler> {
  const {
    sitemapUrls,
    eventPathIncludes,
    bucket,
    batchSizeEnv,
    defaultBatchSize = 1000,
  } = options

  const requestQueue = await RequestQueue.open()
  const sitemap = await Sitemap.load(sitemapUrls)
  console.log(`Loaded ${sitemap.urls.length} URLs from sitemap: ${sitemapUrls.join(', ')}`)

  const eventUrls = sitemap.urls.filter((url) =>
    eventPathIncludes.some((fragment) => url.includes(fragment))
  )
  console.log(`Filtered ${eventUrls.length} event URLs by ${eventPathIncludes.join(', ')}`)

  const envName = batchSizeEnv || ''
  const batchSize = Number(process.env[envName]) || defaultBatchSize
  console.log(`Queueing in batches of ${batchSize}`)
  for (let i = 0; i < eventUrls.length; i += batchSize) {
    const batch = eventUrls.slice(i, i + batchSize)
    console.log(`Queueing batch ${Math.floor(i / batchSize) + 1}: ${batch.length} URLs`)
    await requestQueue.addRequests(
      batch.map((url) => ({ url, userData: { label: 'detail' } })),
    )
  }

  const { S3_ENDPOINT, S3_REGION, S3_ACCESS_KEY_ID, S3_SECRET_ACCESS_KEY } = process.env
  if (!S3_ENDPOINT || !S3_REGION || !S3_ACCESS_KEY_ID || !S3_SECRET_ACCESS_KEY) {
    throw new Error('S3 configuration environment variables are not fully defined')
  }
  const s3Config: S3ClientConfig = {
    endpoint: S3_ENDPOINT,
    region: S3_REGION,
    credentials: { accessKeyId: S3_ACCESS_KEY_ID, secretAccessKey: S3_SECRET_ACCESS_KEY },
    forcePathStyle: true,
  }
  const s3 = new S3Client(s3Config)

  return new PlaywrightCrawler({
    requestQueue,
    browserPoolOptions: { useFingerprints: false },
    retryOnBlocked: true,
    minConcurrency: 5,
    maxConcurrency: 100,
    maxRequestRetries: 100,
    navigationTimeoutSecs: 60,
    requestHandlerTimeoutSecs: 120,
    requestHandler: async ({ request, page, log }) => {
      const html = await page.content()
      const parsed = new URL(request.loadedUrl)
      let keyPath = `${parsed.hostname}${parsed.pathname}${parsed.search}`
      keyPath = keyPath.replace(/^\/+/, '').replace(/[\?\/&=]/g, '_')
      const s3Key = `${keyPath}.html`
      try {
        await s3.send(
          new PutObjectCommand({
            Bucket: bucket,
            Key: s3Key,
            Body: html,
            ContentType: 'text/html',
          }),
        )
        log.info(`Uploaded HTML to s3://${bucket}/${s3Key}`)
      } catch (err) {
        log.error(`Failed uploading to S3 for ${request.loadedUrl}: ${err}`)
      }
    },
    postNavigationHooks: [async ({ handleCloudflareChallenge }) => {
      await handleCloudflareChallenge()
    }],
  })
}

