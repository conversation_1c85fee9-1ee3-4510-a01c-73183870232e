import 'dotenv/config'
// import { createCrawler } from './crawler-factory.js'
// // import { diceCrawler } from './dice-crawler.js'
import { residentAdvisorCrawler } from './resident-advisor-crawler.js'


// export const diceCrawler = await createCrawler({
//   sitemapUrls: ['https://dice.fm/sitemaps/sitemap.xml'],
//   eventPathIncludes: ['/event/'],
//   bucket: 'dice',
//   batchSizeEnv: 'DICE_BATCH_SIZE',
//   defaultBatchSize: 2000,
// })
async function main() {
    await residentAdvisorCrawler.run()
}

main().catch((err) => {
  console.error(err)
  process.exit(1)
})
