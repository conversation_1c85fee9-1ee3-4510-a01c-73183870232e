import { Play<PERSON><PERSON><PERSON><PERSON>, RequestQueue } from 'crawlee'
import { Sitemap } from '@crawlee/utils'
import { S3Client, PutObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3'
import { URL } from 'url'

const requestQueue = await RequestQueue.open()

const sitemapUrls = ['https://dice.fm/sitemaps/sitemap.xml']
const sitemap = await Sitemap.load(sitemapUrls,undefined, {
    sitemapRetries: 10
})
console.log(`Loaded ${sitemap.urls.length} URLs from DICE sitemap`)
const eventUrls = sitemap.urls.filter((url) => url.includes('/event/'))
console.log(`Filtered ${eventUrls.length} event URLs`)

const BATCH_SIZE = 1000
console.log(`Queueing DICE event URLs in batches of ${BATCH_SIZE}`)
for (let i = 0; i < eventUrls.length; i += BATCH_SIZE) {
  const batch = eventUrls.slice(i, i + BATCH_SIZE)
  console.log(`Queueing DICE batch ${Math.floor(i / BATCH_SIZE) + 1}: ${batch.length} URLs`)
  await requestQueue.addRequests(
    batch.map((url) => ({ url, userData: { label: 'detail' } })),
  )
}
const s3Endpoint = process.env.S3_ENDPOINT
const s3Region = process.env.S3_REGION
const s3AccessKeyId = process.env.S3_ACCESS_KEY_ID
const s3SecretAccessKey = process.env.S3_SECRET_ACCESS_KEY
const s3Bucket = 'dice'

if (!s3Endpoint) {
    throw new Error('S3 configuration environment variables are not fully defined')
}

const s3Config: S3ClientConfig = {
    endpoint: s3Endpoint,
    region: s3Region,
    credentials: {
        accessKeyId: s3AccessKeyId || '',
        secretAccessKey: s3SecretAccessKey || '',
    },
    forcePathStyle: true,
}

const s3 = new S3Client(s3Config)

export const diceCrawler = new PlaywrightCrawler({
    requestQueue,
    retryOnBlocked: true,
    minConcurrency: 5,
    maxConcurrency: 100,
    maxRequestsPerMinute: 60,
    maxRequestRetries: 100,
    navigationTimeoutSecs: 60,
    requestHandlerTimeoutSecs: 120,
    maxRequestsPerCrawl: Number(process.env.MAX_REQUESTS_PER_CRAWL) || Number.MAX_VALUE,
    requestHandler: async ({ request, page, log }) => {
        const html = await page.content()
        const parsed = new URL(request.loadedUrl)
        let keyPath = `${parsed.hostname}${parsed.pathname}${parsed.search}`
        keyPath = keyPath.replace(/^\/+/, '').replace(/[\?\/&=]/g, '_')
        const s3Key = `${keyPath}.html`

        try {
            await s3.send(
                new PutObjectCommand({
                    Bucket: s3Bucket,
                    Key: s3Key,
                    Body: html,
                    ContentType: 'text/html',
                })
            )
            log.info(`Uploaded HTML to s3://${s3Bucket}/${s3Key}`)
        } catch (err) {
            log.error(`Failed uploading to S3 for ${request.loadedUrl}: ${err}`)
        }
    },
    postNavigationHooks: [
        async ({ handleCloudflareChallenge }) => {
            await handleCloudflareChallenge()
        },
    ],
})