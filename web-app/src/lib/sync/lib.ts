import type { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'

export function generateId(): string {
  if (typeof crypto !== 'undefined' && 'randomUUID' in crypto) {
    return crypto.randomUUID()
  }
  return Date.now().toString(36) + Math.random().toString(36).substring(2, 10)
}
export const createLexicalState = (text: string): DefaultTypedEditorState => ({
  root: {
    type: 'root',
    version: 1,
    format: '',
    indent: 0,
    direction: 'ltr',
    children: [
      {
        type: 'paragraph',
        version: 1,
        format: '',
        indent: 0,
        direction: 'ltr',
        textFormat: 0,
        children: [
          { type: 'text', version: 1, format: 0, detail: 0, mode: 'normal', style: '', text },
        ],
      },
    ],
  },
})
export function appendText(state: DefaultTypedEditorState, text: string): DefaultTypedEditorState {
  const textNode = {
    type: 'paragraph' as const,
    version: 1,
    format: '',
    indent: 0,
    direction: 'ltr',
    textFormat: 0,
    textStyle: '',
    children: [
      {
        type: 'text' as const,
        version: 1,
        format: 0,
        detail: 0,
        mode: 'normal' as const,
        style: '',
        text,
      },
    ],
  }

  return {
    ...state,
    root: {
      ...state.root,
      children: [textNode, ...(state.root.children || [])],
    },
  }
}
export function createLexicalStateFromHTML(
  html: string,
): DefaultTypedEditorState {
  const cleaned = html
    .replace(/<a\b(?![^>]*\bhref)[^>]*>/gi, '')
    .replace(/<\/a>/gi, '')                   

  const paras = cleaned
    .split(/<\/p>/i)
    .map((p) => p.replace(/<p[^>]*>/i, '').trim())
    .filter(Boolean)

  const children = paras.map((para) => ({
    type: 'paragraph',
    version: 1,
    children: [
      {
        type: 'text',
        version: 1,
        format: 0,
        detail: 0,
        mode: 'normal',
        style: '',
        text: para,
      },
    ],
  }))

  return {
    root: {
    type: 'root',
    version: 1,
    format: '',
    indent: 0,
    direction: 'ltr',
      children,
    },
  }
}
function detectEmbedType(url: string): { blockType: string; fieldName: string } | null {
  const u = url.toLowerCase()
  if (u.includes('spotify.com/track')) {
    return { blockType: 'Spotify', fieldName: 'spotify' }
  }
  if (u.includes('youtube.com') || u.includes('youtu.be')) {
    return { blockType: 'Youtube', fieldName: 'youtube' }
  }
  if (u.includes('soundcloud.com')) {
    return { blockType: 'SoundCloud', fieldName: 'sound-cloud' }
  }
  if (u.includes('twitter.com')) {
    return { blockType: 'twitter', fieldName: 'twitter' }
  }
  if (u.includes('instagram.com')) {
    return { blockType: 'instagram', fieldName: 'instagram' }
  }
  if (u.includes('dailymotion.com')) {
    return { blockType: 'dailymotion', fieldName: 'dailymotion' }
  }
  return null
}

export function appendEmbedBlock(
  state: DefaultTypedEditorState,
  url: string,
): DefaultTypedEditorState {
  const info = detectEmbedType(url)
  if (!info) {
    return state
  }

  const { blockType, fieldName } = info
  const id = generateId()

  const embedNode = {
    type: 'block' as const,
    version: 2,
    fields: {
      blockType,
      id,
      [fieldName]: url,
      blockName: '',
    },
  }

  return {
    ...state,
    root: {
      ...state.root,
      children: [...(state.root.children || []), embedNode],
    },
  }
}