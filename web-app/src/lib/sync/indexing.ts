import { BasePayload, CollectionSlug, Where } from 'payload'
import { partOf } from '../geoapify/partOf'
import { Hub } from '@/payload-types'

export const INDEXED_COLLECTIONS: CollectionSlug[] = ['events', 'venues', 'festivals']

export const indexObject = async (collection: CollectionSlug, obj: any, payload: BasePayload) => {
  if (!obj) return null

  const { docs } = await payload.find({
    collection,
    where: getCondition(collection, obj),
  })

  let result = docs[0]

  if (docs[0]) {
    result = await update(collection, docs[0], obj, payload)
  } else {
    result = await payload.create({
      collection,
      data: obj,
    })
  }

  return result
}

const getCondition = (collection: CollectionSlug, obj: any): Where => {
  switch (collection) {
    case 'events':
      const type = obj.Location.locationType
      if (obj.Location[type]) {
        const id =
          typeof obj.Location[type] === 'object' ? obj.Location[type].id : obj.Location[type]
        return {
          and: [
            { startDate: { equals: obj.startDate } },
            { [`Location.${type}.id`]: { equals: id } },
          ],
        }
      }
      return {}
    case 'venues':
      return {
        address: { equals: obj.address },
      }
    case 'festivals':
      return {
        name: { equals: obj.name },
      }
    case 'eventOrganizers':
      return {
        name: { equals: obj.name },
      }
    case 'artists':
      return {
        slug: { equals: obj.slug },
      }
    case 'genres':
      return {
        slug: { equals: obj.slug },
      }
    case 'authors':
      return {
        slug: { equals: obj.slug },
      }
    case 'residencies':
      return {
        slug: { equals: obj.slug },
      }
    case 'eventBrands':
      return {
        slug: { equals: obj.slug },
      }
    case 'articles':
      return obj.slug
        ? {
            slug: { equals: obj.slug },
          }
        : {}
    default:
      console.log('This collections is not indexed.')
      return {}
  }
}

const update = async (
  collection: CollectionSlug,
  original: any,
  rawIncoming: any,
  payload: BasePayload,
) => {
  let incoming = rawIncoming
  if (collection === 'venues') {
    incoming = {
      ...rawIncoming,
      hub: await resolveHub(rawIncoming.coordinates[0], rawIncoming.coordinates[1], payload),
    }
  }

  const merged = mergeMissing(original, incoming)
  const updated = await payload.update({
    collection,
    id: original.id,
    data: merged,
  })
  return updated
}

type PlainObject = { [key: string]: any }

function isObjectEqual(a: any, b: any): boolean {
  const cleanA = removeId(a)
  return JSON.stringify(cleanA) === JSON.stringify(b)
}

function removeId(obj: any): any {
  if (Array.isArray(obj)) {
    return obj.map(removeId)
  } else if (obj && typeof obj === 'object') {
    const { id, ...rest } = obj
    return Object.entries(rest).reduce((acc, [key, value]) => {
      acc[key] = removeId(value)
      return acc
    }, {} as any)
  }
  return obj
}

export function mergeMissing<T extends PlainObject>(original: T, incoming: T): T {
  const result: PlainObject = { ...original }

  for (const key in incoming) {
    if ((key === 'overview' || key === 'description') && !!original[key]) {
      result[key] = original[key]
      continue
    }
    const originalVal = original[key]
    const incomingVal = incoming[key]

    if (Array.isArray(originalVal) && Array.isArray(incomingVal)) {
      result[key] = [
        ...originalVal,
        ...incomingVal.filter(
          (incomingItem: any) =>
            !originalVal.some((originalItem: any) => isObjectEqual(originalItem, incomingItem)),
        ),
      ]
    } else if (isObject(originalVal) && isObject(incomingVal)) {
      result[key] = mergeMissing(originalVal, incomingVal)
    } else if (originalVal === undefined || originalVal === null) {
      result[key] = incomingVal
    } else {
      result[key] = originalVal
    }
  }

  return result as T
}

function isObject(value: any): value is PlainObject {
  return typeof value === 'object' && value !== null && !Array.isArray(value)
}

const findKeyPart = (keyPart: string, feature: any) => {
  return Object.keys(feature.properties.datasource.raw).find((key) => key.includes(keyPart))
}

const findOrCreateHub = async (
  hubLevel: 'city' | 'state' | 'country',
  hubName: string,
  hubNameFormatted: string,
  payload: BasePayload,
  hubState?: string,
  hubCountry?: string,
): Promise<Hub> => {
  const existing = await payload.find({
    collection: 'hubs',
    where: {
      formatted: { equals: hubNameFormatted },
    },
  })

  if (!existing.docs[0]) {
    let parent: number | null = null

    if (hubLevel === 'city' && hubState)
      parent = (await findOrCreateHub('state', hubState, hubState, payload, undefined, hubCountry))
        .id
    if (hubLevel === 'state' && hubCountry)
      parent = (await findOrCreateHub('country', hubCountry, hubCountry, payload)).id

    return await payload.create({
      collection: 'hubs',
      data: {
        name: hubName,
        formatted: hubNameFormatted,
        type: hubLevel === 'state' ? 'region' : hubLevel,
        parent,
      },
    })
  }

  return existing.docs[0]
}

const resolveHub = async (lat: number, lon: number, payload: BasePayload) => {
  const locations = await partOf(lat, lon)
  const hubCityLevelFeature = ((locations.features as any[]) || []).find((item) =>
    (item.properties.categories as string[]).includes('administrative.city_level'),
  )
  const hubDistrictLevelFeature = ((locations.features as any[]) || []).find((item) =>
    (item.properties.categories as string[]).includes('administrative.district_level'),
  )

  let hubState = ''
  let hubCountry = ''
  let hubName = ''
  let hubNameFormatted = ''

  if (hubCityLevelFeature) {
    const foundKey = findKeyPart('name:en', hubCityLevelFeature)
    if (foundKey) {
      hubName = hubCityLevelFeature.properties.datasource.raw[foundKey]
      hubNameFormatted = hubCityLevelFeature.properties.formatted
      hubState = hubCityLevelFeature.properties.state
      hubCountry = hubCityLevelFeature.properties.country
    }
  }

  if (!hubCityLevelFeature && hubDistrictLevelFeature) {
    const foundKey = findKeyPart('name:en', hubDistrictLevelFeature)
    if (foundKey) hubName = hubDistrictLevelFeature.properties.datasource.raw[foundKey]
    if (!hubName) hubName = hubDistrictLevelFeature.properties.datasource.raw.name
    hubNameFormatted = hubDistrictLevelFeature.properties.formatted
    hubState = hubDistrictLevelFeature.properties.state
    hubCountry = hubDistrictLevelFeature.properties.country
  }

  if (hubName) {
    return (await findOrCreateHub('city', hubName, hubNameFormatted, payload, hubState, hubCountry))
      .id
  }

  return null
}
