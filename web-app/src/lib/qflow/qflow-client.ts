import fetch from 'node-fetch'
import fetchCookie from 'fetch-cookie'
import { <PERSON>ieJar } from 'tough-cookie'
import * as cheerio from 'cheerio'

const BASE = 'https://dice.qflowhub.io'
const LOGIN_PATH = '/account/signin?ReturnUrl=%2Fevents'
const EVENTS_AJAX_PATH = '/events/ajaxgetjsondata'
const ATTENDEES_AJAX_PATH = '/attendees/ajaxgetjsondata'

const jar = new CookieJar()
export const fetchWithJar = fetchCookie(fetch, jar)

const DOTNET_DATE = /\/Date\((\d+)(?:[+-]\d+)?\)\//
function parseDotNetDate(value: string | null): string | null {
  if (!value) return null
  const m = DOTNET_DATE.exec(value)
  return m ? new Date(Number(m[1])).toISOString() : null
}

async function getCsrfToken(): Promise<string> {
  const res = await fetchWithJar(`${BASE}${LOGIN_PATH}`, {
    headers: { Accept: 'text/html' },
  })
  if (!res.ok) {
    console.error(`Failed to fetch CSRF token: ${res.status} ${res.statusText}`)
    return ''
  }
  const html = await res.text()
  const $ = cheerio.load(html)
  const token = $('input[name="__RequestVerificationToken"]').attr('value')
  if (!token) throw new Error('CSRF token missing')
  return token
}

export async function loginQFlow(user: string, pass: string): Promise<void> {
  const csrf = await getCsrfToken()
  const form = new URLSearchParams({
    __RequestVerificationToken: csrf,
    Email: user,
    Password: pass,
    RememberMe: 'false',
  })

  const res = await fetchWithJar(`${BASE}${LOGIN_PATH}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      Accept: 'text/html',
      Referer: `${BASE}/events`,
    },
    body: form.toString(),
    redirect: 'manual',
  })

  if (res.status !== 302) {
    const body = await res.text()
    throw new Error(`Login failed (${res.status}): ${body.slice(0, 200)}`)
  }
}

export interface QFlowEvent {
  id: string
  name: string
  startDate: string
  endDate: string | null
}

const ajaxHeaders = {
  Accept: 'application/json',
  'X-Requested-With': 'XMLHttpRequest',
  Referer: `${BASE}/events`,
}

export async function fetchAllEvents(): Promise<QFlowEvent[]> {
  const baseParams = {
    draw: '2',
    'columns[0][data]': 'Id',
    'columns[0][name]': '',
    'columns[0][searchable]': 'true',
    'columns[0][orderable]': 'true',
    'columns[0][search][value]': '',
    'columns[0][search][regex]': 'false',

    'columns[1][data]': 'Title',
    'columns[1][name]': '',
    'columns[1][searchable]': 'true',
    'columns[1][orderable]': 'true',
    'columns[1][search][value]': '',
    'columns[1][search][regex]': 'false',

    'columns[2][data]': '2',
    'columns[2][name]': '',
    'columns[2][searchable]': 'true',
    'columns[2][orderable]': 'false',
    'columns[2][search][value]': '',
    'columns[2][search][regex]': 'false',

    'columns[3][data]': 'StartTime',
    'columns[3][name]': '',
    'columns[3][searchable]': 'true',
    'columns[3][orderable]': 'true',
    'columns[3][search][value]': '',
    'columns[3][search][regex]': 'false',

    'order[0][column]': '3',
    'order[0][dir]': 'desc',

    'search[value]': '',
    'search[regex]': 'false',
  }

  const infoParams = new URLSearchParams({
    ...baseParams,
    start: '0',
    length: '1',
    _: Date.now().toString(),
  })
  const infoRes = await fetchWithJar(`${BASE}${EVENTS_AJAX_PATH}?${infoParams.toString()}`, {
    headers: ajaxHeaders,
  })
  const infoJson = (await infoRes.json()) as { recordsTotal: number | string }
  const total = Number(infoJson.recordsTotal) || 0
  if (total === 0) return []

  const allParams = new URLSearchParams({
    ...baseParams,
    start: '0',
    length: String(total),
    _: Date.now().toString(),
  })
  const res = await fetchWithJar(`${BASE}${EVENTS_AJAX_PATH}?${allParams.toString()}`, {
    headers: ajaxHeaders,
  })
  const json = (await res.json()) as { data: any }
  if (!Array.isArray(json.data)) {
    throw new Error('Events response missing .data[]')
  }

  return json.data.map((row: any) => ({
    id: String(row.Id),
    name: String(row.Title),
    startDate: parseDotNetDate(row.StartTime) ?? '',
    endDate: parseDotNetDate(row.EndTime),
  }))
}

export function filterRecent(events: QFlowEvent[], days = 1): QFlowEvent[] {
  if (!Array.isArray(events)) {
    throw new Error('filterRecent: not an array')
  }
  const now = Date.now()
  const cutoff = days * 24 * 60 * 60 * 1000
  return events.filter((e) => {
    const ts = new Date(e.endDate ?? e.startDate).getTime()
    return now - ts < cutoff
  })
}

export interface QFlowAttendee {
  ticketId: string
  checkedIn: boolean
  checkInTime: string | null
}

export async function fetchAttendees(eventId: string): Promise<QFlowAttendee[]> {
  const baseParams: Record<string, string> = {
    draw: '1',

    'columns[0][data]': 'FirstName',
    'columns[0][name]': '',
    'columns[0][searchable]': 'true',
    'columns[0][orderable]': 'true',
    'columns[0][search][value]': '',
    'columns[0][search][regex]': 'false',

    'columns[1][data]': 'LastName',
    'columns[1][name]': '',
    'columns[1][searchable]': 'true',
    'columns[1][orderable]': 'true',
    'columns[1][search][value]': '',
    'columns[1][search][regex]': 'false',

    'columns[2][data]': 'Tags',
    'columns[2][name]': '',
    'columns[2][searchable]': 'true',
    'columns[2][orderable]': 'true',
    'columns[2][search][value]': '',
    'columns[2][search][regex]': 'false',

    'columns[3][data]': 'Admitted',
    'columns[3][name]': '',
    'columns[3][searchable]': 'true',
    'columns[3][orderable]': 'true',
    'columns[3][search][value]': '',
    'columns[3][search][regex]': 'false',

    'order[0][column]': '0',
    'order[0][dir]': 'asc',

    'search[value]': '',
    'search[regex]': 'false',
    extra_search: '',
  }

  let params = new URLSearchParams({
    ...baseParams,
    isGrouping: 'False',
    start: '0',
    length: '1',
    _: Date.now().toString(),
  })
  const infoRes = await fetchWithJar(
    `${BASE}${ATTENDEES_AJAX_PATH}/${eventId}?${params.toString()}`,
    { headers: ajaxHeaders },
  )
  const info = (await infoRes.json()) as { recordsTotal: number | string }
  const total = Number(info.recordsTotal) || 0
  if (total === 0) return []

  params = new URLSearchParams({
    ...baseParams,
    isGrouping: 'False',
    start: '0',
    length: String(total),
    _: Date.now().toString(),
  })
  const res = await fetchWithJar(`${BASE}${ATTENDEES_AJAX_PATH}/${eventId}?${params.toString()}`, {
    headers: ajaxHeaders,
  })
  const json = (await res.json()) as { data: any }
  if (!Array.isArray(json.data)) {
    console.error('Attendees response missing .data[]', json)
      return []
  }

  return json.data.map((r: any) => ({
    ticketId: String(r.Barcode),
    checkedIn: r.Admitted !== null && r.Admitted !== undefined,
    checkInTime: r.Admitted ? new Date(r.Admitted).toISOString() : null,
  }))
}
