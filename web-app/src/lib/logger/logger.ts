// import pino, { Logger } from 'pino'
// import pinoElasticSearch from 'pino-elasticsearch'

// const streamToElastic = pinoElasticSearch({
//   index: 'app-logs',
//   node: `https://observe.lumexagency.app/api/default/`,
//   esVersion: 7,
//   flushBytes: 1000,
//   auth: {
//     // apiKey: process.env.OPENOBSERVE_ACCESS_KEY!
//     username: process.env.OPENOBSERVE_USERNAME!,
//     password: process.env.PASSWORD!,
//   },
// })

// export const logger: Logger =
//   process.env['NODE_ENV'] === 'production'
//     ? pino(
//         {
//           level: 'info',
//         },
//         // new OpenobserveTransport({
//         //   url: process.env.OPENOBSERVE_URL!,
//         //   organization: 'default',
//         //   streamName: 'app-logs',
//         //   auth: {
//         //     username: process.env.OPENOBSERVE_USERNAME!,
//         //     password: process.env.PASSWORD!,
//         //   },
//         // }),
//         streamToElastic,
//       )
//     : pino({
//         transport: {
//           target: 'pino-pretty',
//           options: {
//             colorize: true,
//           },
//         },
//         level: 'debug',
//       })
