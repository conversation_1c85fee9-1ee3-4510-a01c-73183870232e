import { BasePayload, CollectionSlug, File } from 'payload'
import { sanityCollections } from './get-raw-data'
import {
  Article,
  Artist,
  Author,
  Event,
  EventBrand,
  FestivalProfile,
  Genre,
  Residency,
  Venue,
} from '@/payload-types'
import { formatSlug } from '@/fields/slug/formatSlug'
import { tzIds } from '@/constants/timezones'
import { indexObject } from '../sync/indexing'
import { appendEmbedBlock } from '../sync/lib'
import { DefaultTypedEditorState } from '@payloadcms/richtext-lexical'
import { getArtistData } from '../spotify/handle-artist'
import { handleImageUpload } from '../media/lib'

export type IDMap = Record<string, number>
export type TransformerFunc = (
  doc: any,
  idMap: IDMap,
  collection: SanityCollectionNames,
  allData: Record<SanityCollectionNames, any[]>,
  payload: BasePayload,
) => any
export type SanityCollectionNames = (typeof sanityCollections)[number]

const resolveRef = async (
  ref: string,
  collection: SanityCollectionNames,
  idMap: IDMap,
  allData: Record<SanityCollectionNames, any[]>,
  payload: BasePayload,
) => {
  if (ref && idMap[ref]) {
    return idMap[ref]
  }
  const doc = allData[collection]?.find((item) => item._id === ref)

  return await importToPayload(collection, doc, idMap, payload, allData)
}

const resolveRefs = async (
  arr: string[],
  collection: SanityCollectionNames,
  idMap: IDMap,
  allData: Record<SanityCollectionNames, any[]>,
  payload: BasePayload,
) =>
  (
    await Promise.all(arr.map((val) => resolveRef(val, collection, idMap, allData, payload)))
  ).filter((item): item is number => item !== null)

const resolveImg = async (ref: string, payload: BasePayload, alt?: string) => {
  const imgParts = ref.split('-')
  const imgUrl = `https://cdn.sanity.io/images/pge26oqu/production/${imgParts[1]}-${imgParts[2]}.${imgParts[3]}`

  const result = (await handleImageUpload(imgUrl, imgParts[1]!, imgParts[1]!, payload)).id

  return result
}

const resolveMentions = async (
  arr: string[],
  idMap: IDMap,
  allData: Record<SanityCollectionNames, any[]>,
  payload: BasePayload,
) => {
  const result: { relationTo: CollectionSlug; value: number }[] = []

  for (const ref of arr) {
    let foundKey: SanityCollectionNames | undefined
    let foundValue: any

    for (const [key, items] of Object.entries(allData)) {
      if (key === 'genre') continue
      const match = (items as any[]).find((item: any) => item._id === ref)
      if (match) {
        foundKey = key as SanityCollectionNames
        foundValue = match
        break
      }
    }

    if (foundKey && foundValue) {
      const payloadCollection: CollectionSlug =
        foundKey === 'residency' ? 'residencies' : `${foundKey}s`
      const value = await resolveRef(ref, foundKey, idMap, allData, payload)

      if (value) result.push({ relationTo: payloadCollection, value })
    }
  }

  return result
}

const collectSocialLinks = (doc: any) => {
  const resources: Record<
    string,
    | 'Facebook'
    | 'Instagram'
    | 'TikTok'
    | 'SoundCloud'
    | 'Discord'
    | 'YouTube'
    | 'Twitter'
    | 'Twitch'
    | 'Pinterest'
    | 'Spotify'
    | 'BeatPort'
    | 'Website'
    | 'Dice'
  > = {
    facebookUrl: 'Facebook',
    instagramUrl: 'Instagram',
    soundCloudUrl: 'SoundCloud',
    twitterUrl: 'Twitter',
    beatportUrl: 'BeatPort',
    youTubeUrl: 'YouTube',
    twitchUrl: 'Twitch',
    websiteUrl: 'Website',
  }

  return Object.entries(resources)
    .map(([key, value]) => {
      return doc[key]
        ? {
            resource: value,
            link: doc[key] as string,
          }
        : undefined
    })
    .filter(Boolean) as {
    resource:
      | 'Facebook'
      | 'Instagram'
      | 'TikTok'
      | 'SoundCloud'
      | 'Discord'
      | 'YouTube'
      | 'Twitter'
      | 'Twitch'
      | 'Pinterest'
      | 'Spotify'
      | 'BeatPort'
      | 'Website'
      | 'Dice'
    link: string
  }[]
}

const resolveBlock = (block: any) => {
  return block.children
    .map((child: any) => {
      return child._type === 'span'
        ? {
            type: 'text',
            version: 1,
            format: 0,
            detail: 0,
            mode: 'normal',
            style: '',
            text: child.text,
          }
        : null
    })
    .filter(Boolean)
}

const resolveEmbeding = (state: DefaultTypedEditorState, url: string) => {
  return appendEmbedBlock(state, url)
}

const resolveImageBlock = async (
  state: DefaultTypedEditorState,
  imgRef: string,
  payload: BasePayload,
) => {
  const imageBlock = {
    type: 'upload',
    fields: null,
    format: '',
    version: 3,
    relationTo: 'media',
    value: await resolveImg(imgRef, payload),
  }

  return {
    ...state,
    root: {
      ...state.root,
      children: [...(state.root.children || []), imageBlock],
    },
  }
}

type RichText = {
  root: {
    type: string
    children: {
      type: string
      version: number
      [k: string]: unknown
    }[]
    direction: ('ltr' | 'rtl') | null
    format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
    indent: number
    version: number
  }
  [k: string]: unknown
} | null

const convertRichTextField = async (blocks: any[], payload: BasePayload): Promise<RichText> => {
  let richText = {
    root: {
      type: 'root',
      version: 1,
      format: '',
      indent: 0,
      direction: 'ltr',
      children: [
        {
          type: 'paragraph',
          version: 1,
          format: '',
          indent: 0,
          direction: 'ltr',
          textFormat: 0,
          children: [
            { type: 'text', version: 1, format: 0, detail: 0, mode: 'normal', style: '', text: '' },
          ],
        },
      ],
    },
  } satisfies RichText
  richText.root.children.shift()

  const sampleParagraph = {
    type: 'paragraph',
    version: 1,
    format: '',
    indent: 0,
    direction: 'ltr',
    textFormat: 0,
    children: [
      { type: 'text', version: 1, format: 0, detail: 0, mode: 'normal', style: '', text: '' },
    ],
  }

  for (const block of blocks) {
    if (block._type === 'block') {
      const paragraph = structuredClone(sampleParagraph)
      paragraph.children.shift()
      const spans = [] as {
        type: string
        version: number
        format: number
        detail: number
        mode: string
        style: string
        text: string
      }[]
      spans.push(resolveBlock(block))
      paragraph.children = spans.flat()
      richText = {
        ...richText,
        root: {
          ...richText.root,
          children: [...(richText.root.children || []), paragraph],
        },
      }
    }

    if (block._type === 'image' && block.asset?._ref)
      (richText as DefaultTypedEditorState) = await resolveImageBlock(
        richText,
        block.asset._ref,
        payload,
      )

    if (
      block._type === 'soundCloudEmbed' ||
      block._type === 'spotifyEmbed' ||
      block._type === 'instagramEmbed' ||
      block._type === 'twitterEmbed'
    )
      (richText as DefaultTypedEditorState) = resolveEmbeding(richText, block.url)

    if (block._type === 'videoEmbed' && block.platform === 'youtube')
      (richText as DefaultTypedEditorState) = resolveEmbeding(
        richText,
        `https://youtu.be/${block.embedId}`,
      )
  }

  return richText
}

export async function importToPayload(
  collection: SanityCollectionNames,
  doc: any,
  idMap: IDMap,
  payload: BasePayload,
  allData: Record<SanityCollectionNames, any[]>,
): Promise<number | null> {
  if (!doc) return null
  if (collection === 'event' && (doc.isVirtualEvent || (!doc.venue && doc.venuePlaceholder)))
    return null // corner case
  const payloadCollection: CollectionSlug =
    collection === 'residency' ? 'residencies' : `${collection}s`

  const transformer = transformers[collection]
  const transformed = await transformer(doc, idMap, collection, allData, payload)

  const indexedObject = await indexObject(payloadCollection, transformed, payload)

  return indexedObject ? indexedObject.id : null
}

const transformGenre: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  return {
    name: doc.name,
    slug: formatSlug(doc.name),
    type: 'GrayArea',
  } satisfies Omit<Genre, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformAuthor: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  const country = (
    await payload.find({
      collection: 'countries',
      where: {
        code: { equals: doc.country },
      },
    })
  ).docs[0]?.id

  return {
    name: doc.name,
    slug: doc.slug.current,
    country,
    previewImage: doc.photo
      ? await resolveImg(doc.photo.asset._ref, payload, doc.slug.current)
      : null,
    overview: {
      overview: {
        content: doc.bio ? await convertRichTextField(doc.bio, payload) : null,
      },
    },
    socialLinks: collectSocialLinks(doc),
  } satisfies Omit<Author, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformVenue: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  return {
    externalSanityId: doc._id,
    origin: 'sanity',
    name: doc.name,
    slug: doc.slug.current,
    address: doc.location.streetAddress ?? 'No address provided',
    city: doc.location.city,
    country: doc.location.country,
    coordinates: [doc.location.latitude, doc.location.longitude],
    timezone: doc.location.timezone,
    previewImage: doc.photo
      ? await resolveImg(doc.photo.asset._ref, payload, doc.slug.current)
      : null,
    overview: {
      overview: {
        content: doc.description ? await convertRichTextField(doc.description, payload) : null,
      },
    },
    socialLinks: collectSocialLinks(doc),
  } satisfies Omit<Venue, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformResidency: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  return {
    name: doc.name,
    slug: doc.slug.current,
    Location: {
      venue:
        typeof doc.venue === 'number'
          ? doc.venue
          : await resolveRef(doc.venue._ref, 'venue', idMap, allData, payload),
    },
    previewImage: doc.photo
      ? await resolveImg(doc.photo.asset._ref, payload, doc.slug.current)
      : null,
    eventBrand: doc.eventBrand
      ? typeof doc.eventBrand === 'number'
        ? doc.eventBrand
        : await resolveRef(doc.eventBrand._ref, 'eventBrand', idMap, allData, payload)
      : null,
    overview: {
      overview: {
        content: doc.description ? await convertRichTextField(doc.description, payload) : null,
      },
    },
    socialLinks: collectSocialLinks(doc),
  } satisfies Omit<Residency, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformEventBrand: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  const country = (
    await payload.find({
      collection: 'countries',
      where: {
        code: { equals: doc.country },
      },
    })
  ).docs[0]?.id

  return {
    name: doc.name,
    slug: doc.slug.current,
    country,
    previewImage: doc.photo
      ? await resolveImg(doc.photo.asset._ref, payload, doc.slug.current)
      : null,
    overview: {
      overview: {
        content: doc.description ? await convertRichTextField(doc.description, payload) : null,
      },
    },
    socialLinks: collectSocialLinks(doc),
  } satisfies Omit<EventBrand, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformFestival: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  return {
    externalSanityId: doc._id,
    origin: 'sanity',
    name: doc.name,
    slug: doc.slug.current,
    previewImage: doc.backgroundImage
      ? await resolveImg(doc.backgroundImage.asset._ref, payload, doc.slug.current)
      : null,
    overview: {
      overview: {
        content: doc.description ? await convertRichTextField(doc.description, payload) : null,
      },
    },
    socialLinks: collectSocialLinks(doc),
  } satisfies Omit<FestivalProfile, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformArtist: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  if (!doc.spotifyId) return null
  const country = (
    await payload.find({
      collection: 'countries',
      where: {
        code: { equals: doc.country },
      },
    })
  ).docs[0]?.id
  const genresRefs = doc.genres?.map((g: any) => g._ref)
  const genres = genresRefs ? await resolveRefs(genresRefs, 'genre', idMap, allData, payload) : null

  const artistData = await getArtistData(doc.spotifyId, payload)

  return {
    ...artistData,
    externalSanityId: doc._id,
    name: doc.name,
    slug: doc.slug ? doc.slug.current : formatSlug(doc.name),
    spotifyId: doc.spotifyId,
    country: artistData.country ? artistData.country : country,
    genres: artistData.genres ? artistData.genres : genres,
    previewImage: artistData.previewImage
      ? artistData.previewImage
      : doc.photo && doc.photo.asset
        ? await resolveImg(doc.photo.asset._ref, payload, doc.slug.current)
        : null,
    overview: {
      overview: {
        content: doc.bio
          ? await convertRichTextField(doc.bio, payload)
          : (artistData.overview as RichText),
      },
    },
    socialLinks: artistData.socialLinks ? artistData.socialLinks : collectSocialLinks(doc),
  } satisfies Omit<Artist, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

const transformArticle: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  let type: 'Academy' | 'Magazine' | null
  switch (doc.type) {
    case 'academy':
      type = 'Academy'
      break
    case 'artistSpotlight':
      type = null
      break
    default:
      type = 'Magazine'
      break
  }
  const articleMainEntityRef = doc.entity
    ? (await resolveMentions([doc.entity._ref], idMap, allData, payload))[0]
    : null
  const articleMainEntity: any = articleMainEntityRef
    ? (
        await payload.find({
          collection: articleMainEntityRef?.relationTo,
          where: {
            id: { equals: articleMainEntityRef.value },
          },
        })
      ).docs[0]
    : null
  const typeName = doc.type
    .split(/(?=[A-Z])/)
    .map((word: string) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
  const generatedTitle = `${typeName} for ${articleMainEntity?.name || articleMainEntity?.id}`
  const mentions = doc.entities?.map((entity: any) => entity._ref)

  const author = doc.author
    ? ((await resolveRef(doc.author._ref, 'author', idMap, allData, payload)) as number)
    : ((
        await payload.find({
          collection: 'authors',
        })
      ).docs[0]?.id as number)
  const slug = doc.slug ? doc.slug.current : formatSlug(doc.title ? doc.title : generatedTitle)

  return type
    ? ({
        type,
        title: doc.title ? doc.title : generatedTitle,
        slug,
        author,
        previewImage:
          doc.image && doc.image.asset
            ? await resolveImg(doc.image.asset._ref, payload, slug)
            : null,
        // TODO: resolve hub
        hubs: doc.hubs ? null : null,
        // @ts-ignore
        mentions: mentions ? await resolveMentions(mentions, idMap, allData, payload) : null,
        overview: {
          overview: {
            content: doc.body ? await convertRichTextField(doc.body, payload) : null,
          },
        },
      } satisfies Omit<Article, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>)
    : null
}

const transformEvent: TransformerFunc = async (doc, idMap, collection, allData, payload) => {
  const lineupRefs = doc.artists ? (doc.artists as any[]).map((artist: any) => artist._ref) : []
  const lineup = (await resolveRefs(lineupRefs, 'artist', idMap, allData, payload)).map(
    (artist: number) => ({
      artist,
    }),
  )
  const venueId = doc.venue
    ? await resolveRef(doc.venue._ref, 'venue', idMap, allData, payload)
    : null
  const venueTimeZone = venueId
    ? (
        await payload.find({
          collection: 'venues',
          where: {
            id: { equals: venueId },
          },
          select: {
            timezone: true,
          },
        })
      ).docs[0]?.timezone
    : null

  return {
    externalSanityId: doc._id,
    origin: 'sanity',
    name: doc.name,
    slug: doc.slug.current,
    ticketUrls: doc.ticketUrl ? [doc.ticketUrl] : [],
    Location: doc.venue
      ? {
          // TODO: if no venue provided add hub (city)
          venue: venueId,
          locationType: 'venue',
        }
      : undefined,
    startDate: doc.startDate,
    endDate: doc.endDate ? doc.endDate : doc.startDate,
    previewImage:
      doc.imageSquare && doc.imageSquare.asset
        ? await resolveImg(doc.imageSquare.asset._ref, payload, doc.slug.current)
        : null,
    socialLinks: collectSocialLinks(doc),
    lineup: doc.artists ? lineup : null,
    residency: doc.residency
      ? await resolveRef(doc.residency._ref, 'residency', idMap, allData, payload)
      : null,
    eventBrand: doc.eventBrand
      ? await resolveRef(doc.eventBrand._ref, 'eventBrand', idMap, allData, payload)
      : null,
    festival: doc.festival
      ? await resolveRef(doc.festival._ref, 'festival', idMap, allData, payload)
      : null,
    overview: {
      overview: {
        content: doc.description ? await convertRichTextField(doc.description, payload) : null,
      },
    },
    timezone:
      venueTimeZone && tzIds.includes(venueTimeZone) ? (venueTimeZone as Event['timezone']) : null,
  } satisfies Omit<Event, 'sizes' | 'createdAt' | 'updatedAt' | 'id'>
}

export const transformers: Record<SanityCollectionNames, TransformerFunc> = {
  event: transformEvent,
  artist: transformArtist,
  article: transformArticle,
  author: transformAuthor,
  eventBrand: transformEventBrand,
  festival: transformFestival,
  genre: transformGenre,
  residency: transformResidency,
  venue: transformVenue,
}
