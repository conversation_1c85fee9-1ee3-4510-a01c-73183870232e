import { NextResponse } from 'next/server'
import { BasePayload, CollectionSlug, PayloadRequest } from 'payload'
import { getSanytiData, sanityCollections } from './get-raw-data'
import { IDMap, importToPayload, SanityCollectionNames } from './transformers'
import { getFromObjectStorage, uploadToObjectStorage } from '../s3/client'

const collections: CollectionSlug[] = [
  'events',
  'genres',
  'authors',
  'eventBrands',
  'festivals',
  'hubs',
  'ochoEpisodes',
  'residencies',
  'venues',
  'articles',
  'artists',
]

export async function resolveRefs(
  obj: any,
  idMap: IDMap,
  allData: Record<SanityCollectionNames, any[]>,
  payload: BasePayload,
): Promise<any> {
  if (Array.isArray(obj)) {
    return await Promise.all(obj.map((item) => resolveRefs(item, idMap, allData, payload)))
  }

  if (
    obj &&
    typeof obj === 'object' &&
    obj._type !== 'image' &&
    obj._type !== 'block' &&
    obj._type !== 'reference' &&
    (obj._type as string).includes('Embed')
  ) {
    if (obj._ref && typeof obj._ref === 'string') {
      const refId = obj._ref

      if (!idMap[refId]) {
        const [collection, item] = findItemInAllData(refId, allData)

        if (!collection || !item) {
          payload.logger.error(`Reference not found for ID: ${refId}`)
          return null
        }

        const resolved = await resolveRefs(item, idMap, allData, payload)
        const newId = await importToPayload(
          collection as SanityCollectionNames,
          resolved,
          idMap,
          payload,
          allData,
        )
        if (newId) idMap[refId] = newId
      }

      return idMap[refId]
    }

    const newObj: any = {}
    for (const key of Object.keys(obj)) {
      newObj[key] = await resolveRefs(obj[key], idMap, allData, payload)
    }
    return newObj
  }

  return obj
}

export function findItemInAllData(
  refId: string,
  allData: Record<SanityCollectionNames, any[]>,
): [string | null, any | null] {
  for (const [collection, items] of Object.entries(allData)) {
    const found = items.find((item) => item._id === refId)
    if (found) return [collection, found]
  }
  return [null, null]
}

export const seedSanityData = async (req: PayloadRequest) => {
  const { payload } = req

  try {
    payload.logger.info('Seeding Sanity data...')
    payload.logger.info('— Seeding collections...')

    const allData: Record<string, any[]> = {}
    const idMap: IDMap = {}

    for (const collection of sanityCollections) {
      const key = `sanity-${collection}`
      getSanytiData(collection).then(async (data) => {
        await uploadToObjectStorage(JSON.stringify(data), key, 'sanity', 'application/json')
      })
    }

    for (const collection of sanityCollections) {
      const rawSanityData = await getFromObjectStorage(`sanity-${collection}`, 'sanity')
      allData[collection] = JSON.parse(rawSanityData)
    }

    for (const collection of sanityCollections) {
      const items = allData[collection]

      for (const item of items || []) {
        if (!idMap[item._id]) {
          const resolved = await resolveRefs(item, idMap, allData, payload)
          const newId = await importToPayload(collection, resolved, idMap, payload, allData)
          if (newId) idMap[item._id] = newId
        }
      }

      payload.logger.info(`— ${collection} seeded`)
    }

    return NextResponse.json({ message: 'Sanity data successfully seeded' }, { status: 200 })
  } catch (error) {
    console.log('Something went wrong wile seeding Sanity data. ', error)
    return NextResponse.json({ error: error }, { status: 500 })
  }
}
