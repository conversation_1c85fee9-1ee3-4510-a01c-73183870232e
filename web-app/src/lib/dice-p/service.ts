// lib/dice/service.ts
import { GraphQLClient } from 'graphql-request'
import { getSdk, Sdk } from './schema-types'
export default class DicePService {
  private sdk: Sdk

  constructor(token: string, endpoint: string = 'https://p-api.dice.fm/graphql') {
    const client = new GraphQLClient(endpoint, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    this.sdk = getSdk(client)
  }
  public getEventWithTicketTypes(variables: Parameters<Sdk['getEventWithTicketTypes']>[0]) {
    return this.sdk.getEventWithTicketTypes(variables)
  }
  public getEventTrackingLinks(variables: Parameters<Sdk['getEventTrackingLinks']>[0]) {
    return this.sdk.getEventTrackingLinks(variables)
  }
  public getEventPromotions(variables: Parameters<Sdk['getEventPromotions']>[0]) {
    return this.sdk.getEventPromotions(variables)
  }
}
