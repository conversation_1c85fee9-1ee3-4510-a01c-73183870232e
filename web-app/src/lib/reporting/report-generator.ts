import Handlebars from 'handlebars'
import type { Payload } from 'payload'
import { SalesService, type RecipientData } from './sales.service'

Handlebars.registerHelper('formatCurrency', (value: number) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
  }).format(value)
})

const EMAIL_TEMPLATE = `<html lang='en'>
  <head>
    <meta charset='UTF-8' />
    <meta name='viewport' content='width=device-width, initial-scale=1.0' />
    <style>
      body {
        font-family:
          -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
          'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
        color: #333;
        background-color: #f4f4f4;
        margin: 0;
        padding: 20px;
      }
      .container {
        max-width: 680px;
        margin: 0 auto;
        background-color: #ffffff;
        border: 1px solid #e1e1e1;
        border-radius: 8px;
        overflow: hidden;
      }
      .header {
        background-color: #f8f8f8;
        padding: 24px;
        border-bottom: 1px solid #e1e1e1;
        text-align: center;
      }
      .header h1 {
        margin: 0;
        font-size: 24px;
        color: #111;
      }
      .header p {
        margin: 4px 0 0;
        color: #777;
      }
      .content {
        padding: 24px;
      }
      .event-section {
        margin-bottom: 32px;
      }
      .event-section:last-child {
        margin-bottom: 0;
      }
      .event-details {
        margin-bottom: 16px;
        padding-bottom: 16px;
        border-bottom: 1px solid #f0f0f0;
      }
      .event-details p {
        margin: 4px 0;
        font-size: 16px;
      }
      .event-details strong {
        color: #555;
      }
      .sales-table {
        width: 100%;
        border-collapse: collapse;
      }
      .sales-table caption {
        font-size: 18px;
        font-weight: bold;
        margin-bottom: 12px;
        text-align: left;
        color: #333;
      }
      .sales-table th,
      .sales-table td {
        border: 1px solid #e1e1e1;
        padding: 12px;
        text-align: left;
        font-size: 14px;
      }
      .sales-table th {
        background-color: #f9f9f9;
        font-weight: 600;
      }
      .total-row td {
        font-weight: bold;
        background-color: #f9f9f9;
      }
      .text-right {
        text-align: right;
      }
      .footer {
        text-align: center;
        font-size: 12px;
        color: #999;
        padding: 20px;
        background-color: #f8f8f8;
        border-top: 1px solid #e1e1e1;
      }
    </style>
  </head>
  <body>
    <div class='container'>
      <div class='header'>
        <h1>Gray Area Sales Report</h1>
        <p>Report Date: {{reportDate}}</p>
      </div>
      <div class='content'>
        {{#each events}}
          <div class='event-section'>
            <div class='event-details'>
              <p><strong>Event:</strong> {{this.eventName}}</p>
              <p><strong>Date:</strong> {{this.eventDate}}</p>
              <p><strong>Artist:</strong> {{this.artistName}}</p>
              <p><strong>Venue:</strong> {{this.venueName}}</p>
            </div>

            <table class='sales-table'>
              <caption>Ticket Sales by Type</caption>
              <thead>
                <tr>
                  <th>Ticket Type</th>
                  <th class='text-right'>Tickets Sold</th>
                  <th class='text-right'>Face Value</th>
                  <th class='text-right'>Total Gross</th>
                </tr>
              </thead>
              <tbody>
                {{#each this.ticketSales}}
                  <tr>
                    <td>{{this.ticketTypeName}}</td>
                    <td class='text-right'>{{this.ticketsSold}}</td>
                    <td class='text-right'>{{formatCurrency this.faceValue}}</td>
                    <td class='text-right'>{{formatCurrency this.totalGrossValue}}</td>
                  </tr>
                {{/each}}
              </tbody>
              <tfoot>
                <tr class='total-row'>
                  <td><strong>Total</strong></td>
                  <td class='text-right'><strong>{{this.totalTicketsSold}}</strong></td>
                  <td></td>
                  <td class='text-right'><strong>{{formatCurrency
                        this.totalGrossRevenue
                      }}</strong></td>
                </tr>
              </tfoot>
            </table>
          </div>
        {{else}}
          <p>No sales data available for the selected events.</p>
        {{/each}}
      </div>
      <div class='footer'>
        <p>This is an automated report. Please do not reply to this email.</p>
      </div>
    </div>
  </body>
</html>`

export class ReportGenerator {
  private salesService: SalesService
  private emailTemplate: Handlebars.TemplateDelegate

  constructor(private payload: Payload) {
    this.salesService = new SalesService(payload)
    this.emailTemplate = Handlebars.compile(EMAIL_TEMPLATE)
  }

  async generateHtml(recipient: RecipientData): Promise<string | null> {
    if (recipient.events.length === 0) {
      return null
    }

    const templateData = {
      reportDate: new Date().toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
      }),
      events: recipient.events,
    }

    return this.emailTemplate(templateData)
  }

  public async sendReportToRecipient(
    recipientId: number,
    recipientType: 'agent' | 'manager',
    isManual: boolean = false,
  ): Promise<{ sent: boolean; skipped: boolean; reason?: string }> {
    const recipients = await this.salesService.getRecipientsWithReporting()
    const recipient = recipients.find(
      (r) => r.recipientId === recipientId && r.recipientType === recipientType,
    )

    if (!recipient) {
      this.payload.logger.warn(
        `Recipient not found or has reporting disabled: ${recipientType} ${recipientId}`,
      )
      return { sent: false, skipped: true, reason: 'Recipient not found or reporting disabled' }
    }

    recipient.events = await this.salesService.getEventsForRecipient(
      recipientId,
      recipientType,
      isManual,
    )

    if (recipient.events.length === 0) {
      const reason = isManual ? 'No events found' : 'No events match daily reporting criteria'
      this.payload.logger.info(`${reason} for ${recipient.recipientEmail}. No report sent.`)
      return { sent: false, skipped: true, reason }
    }

    const html = await this.generateHtml(recipient)

    if (html) {
      await this.payload.sendEmail({
        to: recipient.recipientEmail,
        subject: `Gray Area Daily Sales Report for ${recipient.recipientName}`,
        html,
      })
      this.payload.logger.info(`Sales report sent to ${recipient.recipientEmail}`)
      return { sent: true, skipped: false }
    } else {
      this.payload.logger.info(
        `Failed to generate HTML for ${recipient.recipientEmail}. No report sent.`,
      )
      return { sent: false, skipped: true, reason: 'Failed to generate HTML' }
    }
  }
}
