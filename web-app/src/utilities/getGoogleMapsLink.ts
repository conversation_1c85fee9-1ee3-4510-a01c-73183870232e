export const getGoogleMapsLink = (
    lat: number,
    lng: number,
    zoom: number = 15,
  ): string => {
    if (!Number.isFinite(lat) || !Number.isFinite(lng)) {
      console.warn("Latitude and longitude must be finite numbers, but received:", lat, lng);
      return  `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&zoom=${zoom}`;
    }
    if (lat < -90 || lat > 90) {
      console.warn("Latitude must be between -90 and 90 degrees, but received:", lat);
      return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&zoom=${zoom}`;
    }
    if (lng < -180 || lng > 180) {
      console.warn("Longitude must be between -180 and 180 degrees, but received:", lng);
      return `https://www.google.com/maps/search/?api=1&query=${lat},${lng}&zoom=${zoom}`;
    }
    const latStr = lat.toString();
    const lngStr = lng.toString();
  
    return `https://www.google.com/maps/search/?api=1&query=${latStr},${lngStr}&zoom=${zoom}`;
  };

