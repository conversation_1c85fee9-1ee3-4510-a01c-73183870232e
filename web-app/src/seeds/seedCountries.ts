import path from 'path'
import { tmpdir } from 'os'
import { promises as fs } from 'fs'
import type { Payload } from 'payload'   

type ApiCountry = {
  name: { common: string }
  cca2: string
  flags: { svg?: string; png?: string }
}

export async function seedCountries(payload: Payload) {
  const { docs } = await payload.find({ collection: 'countries', limit: 1 })
  if (docs.length && !process.env.PAYLOAD_SEED) return 

  const res = await fetch('https://restcountries.com/v3.1/all?fields=name,flags,cca2')
  if (!res.ok) return console.error('Failed to fetch countries:', res.status, res.statusText)
  const countries: ApiCountry[] = await res.json()

  for (const c of countries) {
    const flagURL = c.flags.svg ?? c.flags.png
    if (!flagURL) continue

    try {
      const buf      = Buffer.from(await (await fetch(flagURL)).arrayBuffer())
      const filename = path.basename(new URL(flagURL).pathname)
      const tmpPath  = path.join(tmpdir(), filename)
      await fs.writeFile(tmpPath, buf)

      const media = await payload.create({
        collection: 'media',
        filePath:   tmpPath,
        data: { alt: c.name.common },
        overrideAccess: true,
      })
      await fs.unlink(tmpPath)

      await payload.create({
        collection: 'countries',
        data: { name: c.name.common, code: c.cca2, flag: media.id },
        overrideAccess: true,
      })
    } catch (e) {
      console.warn(`skip ${c.name.common}:`, (e as Error).message)
    }
  }

  console.info('🫡 Countries seeding finished')
}
