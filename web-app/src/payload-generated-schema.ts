/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:db-schema` to regenerate this file.
 */

import {
  pgTable,
  index,
  uniqueIndex,
  foreignKey,
  serial,
  varchar,
  timestamp,
  integer,
  boolean,
  jsonb,
  numeric,
  type AnyPgColumn,
  text,
  pgEnum,
} from '@payloadcms/db-postgres/drizzle/pg-core'
import { sql, relations } from '@payloadcms/db-postgres/drizzle'
import { geometryColumn } from '@payloadcms/db-postgres'
export const enum_agents_title = pgEnum('enum_agents_title', [
  'partner',
  'regular',
  'junior',
  'coordinator',
  'assistant',
])
export const enum_articles_type = pgEnum('enum_articles_type', ['Academy', 'Magazine'])
export const enum_artist_deals_status_history_status = pgEnum(
  'enum_artist_deals_status_history_status',
  ['offerSent', 'counterOffer', 'declined', 'accepted', 'canceled'],
)
export const enum_artists_social_links_resource = pgEnum('enum_artists_social_links_resource', [
  'Facebook',
  'Instagram',
  'TikTok',
  'SoundCloud',
  'Discord',
  'YouTube',
  'Twitter',
  'Twitch',
  'Pinterest',
  'Spotify',
  'BeatPort',
  'Website',
  'Dice',
  'TVMaze',
  'MusicBrainz',
  'Tunefind',
  'Line',
  'Genius',
  'Pandora',
  'Shazam',
  'Tidal',
  'LastFm',
  'Deezer',
  'Songkick',
  'Bandsintown',
  'Discogs',
  'Itunes',
  'Amazon',
])
export const enum_authors_social_links_resource = pgEnum('enum_authors_social_links_resource', [
  'Facebook',
  'Instagram',
  'TikTok',
  'SoundCloud',
  'Discord',
  'YouTube',
  'Twitter',
  'Twitch',
  'Pinterest',
  'Spotify',
  'BeatPort',
  'Website',
  'Dice',
  'TVMaze',
  'MusicBrainz',
  'Tunefind',
  'Line',
  'Genius',
  'Pandora',
  'Shazam',
  'Tidal',
  'LastFm',
  'Deezer',
  'Songkick',
  'Bandsintown',
  'Discogs',
  'Itunes',
  'Amazon',
])
export const enum_event_brands_social_links_resource = pgEnum(
  'enum_event_brands_social_links_resource',
  [
    'Facebook',
    'Instagram',
    'TikTok',
    'SoundCloud',
    'Discord',
    'YouTube',
    'Twitter',
    'Twitch',
    'Pinterest',
    'Spotify',
    'BeatPort',
    'Website',
    'Dice',
    'TVMaze',
    'MusicBrainz',
    'Tunefind',
    'Line',
    'Genius',
    'Pandora',
    'Shazam',
    'Tidal',
    'LastFm',
    'Deezer',
    'Songkick',
    'Bandsintown',
    'Discogs',
    'Itunes',
    'Amazon',
  ],
)
export const enum_events_external_platform_source_urls_platform = pgEnum(
  'enum_events_external_platform_source_urls_platform',
  ['sanity', 'dice', 'ra', 'songkick', 'eventbrite'],
)
export const enum_events_lineup_tier = pgEnum('enum_events_lineup_tier', [
  'Headliner',
  'SpecialGuest',
  'SupportI',
  'SupportII',
  'Resident',
  'Local',
  'JrLocal',
])
export const enum_events_social_links_resource = pgEnum('enum_events_social_links_resource', [
  'Facebook',
  'Instagram',
  'TikTok',
  'SoundCloud',
  'Discord',
  'YouTube',
  'Twitter',
  'Twitch',
  'Pinterest',
  'Spotify',
  'BeatPort',
  'Website',
  'Dice',
  'TVMaze',
  'MusicBrainz',
  'Tunefind',
  'Line',
  'Genius',
  'Pandora',
  'Shazam',
  'Tidal',
  'LastFm',
  'Deezer',
  'Songkick',
  'Bandsintown',
  'Discogs',
  'Itunes',
  'Amazon',
])
export const enum_events_marketing_tracking_links_platform = pgEnum(
  'enum_events_marketing_tracking_links_platform',
  ['sanity', 'dice', 'ra', 'songkick', 'eventbrite'],
)
export const enum_events_marketing_tracking_links_deals_params = pgEnum(
  'enum_events_marketing_tracking_links_deals_params',
  ['organic', 'paid'],
)
export const enum_events_origin = pgEnum('enum_events_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_events_location_location_type = pgEnum('enum_events_location_location_type', [
  'venue',
  'hub',
])
export const enum_events_timezone = pgEnum('enum_events_timezone', [
  'Africa/Abidjan',
  'Africa/Accra',
  'Africa/Addis_Ababa',
  'Africa/Algiers',
  'Africa/Asmera',
  'Africa/Bamako',
  'Africa/Bangui',
  'Africa/Banjul',
  'Africa/Bissau',
  'Africa/Blantyre',
  'Africa/Brazzaville',
  'Africa/Bujumbura',
  'Africa/Cairo',
  'Africa/Casablanca',
  'Africa/Ceuta',
  'Africa/Conakry',
  'Africa/Dakar',
  'Africa/Dar_es_Salaam',
  'Africa/Djibouti',
  'Africa/Douala',
  'Africa/El_Aaiun',
  'Africa/Freetown',
  'Africa/Gaborone',
  'Africa/Harare',
  'Africa/Johannesburg',
  'Africa/Juba',
  'Africa/Kampala',
  'Africa/Khartoum',
  'Africa/Kigali',
  'Africa/Kinshasa',
  'Africa/Lagos',
  'Africa/Libreville',
  'Africa/Lome',
  'Africa/Luanda',
  'Africa/Lubumbashi',
  'Africa/Lusaka',
  'Africa/Malabo',
  'Africa/Maputo',
  'Africa/Maseru',
  'Africa/Mbabane',
  'Africa/Mogadishu',
  'Africa/Monrovia',
  'Africa/Nairobi',
  'Africa/Ndjamena',
  'Africa/Niamey',
  'Africa/Nouakchott',
  'Africa/Ouagadougou',
  'Africa/Porto-Novo',
  'Africa/Sao_Tome',
  'Africa/Tripoli',
  'Africa/Tunis',
  'Africa/Windhoek',
  'America/Adak',
  'America/Anchorage',
  'America/Anguilla',
  'America/Antigua',
  'America/Araguaina',
  'America/Argentina/La_Rioja',
  'America/Argentina/Rio_Gallegos',
  'America/Argentina/Salta',
  'America/Argentina/San_Juan',
  'America/Argentina/San_Luis',
  'America/Argentina/Tucuman',
  'America/Argentina/Ushuaia',
  'America/Aruba',
  'America/Asuncion',
  'America/Bahia',
  'America/Bahia_Banderas',
  'America/Barbados',
  'America/Belem',
  'America/Belize',
  'America/Blanc-Sablon',
  'America/Boa_Vista',
  'America/Bogota',
  'America/Boise',
  'America/Buenos_Aires',
  'America/Cambridge_Bay',
  'America/Campo_Grande',
  'America/Cancun',
  'America/Caracas',
  'America/Catamarca',
  'America/Cayenne',
  'America/Cayman',
  'America/Chicago',
  'America/Chihuahua',
  'America/Ciudad_Juarez',
  'America/Coral_Harbour',
  'America/Cordoba',
  'America/Costa_Rica',
  'America/Creston',
  'America/Cuiaba',
  'America/Curacao',
  'America/Danmarkshavn',
  'America/Dawson',
  'America/Dawson_Creek',
  'America/Denver',
  'America/Detroit',
  'America/Dominica',
  'America/Edmonton',
  'America/Eirunepe',
  'America/El_Salvador',
  'America/Fort_Nelson',
  'America/Fortaleza',
  'America/Glace_Bay',
  'America/Godthab',
  'America/Goose_Bay',
  'America/Grand_Turk',
  'America/Grenada',
  'America/Guadeloupe',
  'America/Guatemala',
  'America/Guayaquil',
  'America/Guyana',
  'America/Halifax',
  'America/Havana',
  'America/Hermosillo',
  'America/Indiana/Knox',
  'America/Indiana/Marengo',
  'America/Indiana/Petersburg',
  'America/Indiana/Tell_City',
  'America/Indiana/Vevay',
  'America/Indiana/Vincennes',
  'America/Indiana/Winamac',
  'America/Indianapolis',
  'America/Inuvik',
  'America/Iqaluit',
  'America/Jamaica',
  'America/Jujuy',
  'America/Juneau',
  'America/Kentucky/Monticello',
  'America/Kralendijk',
  'America/La_Paz',
  'America/Lima',
  'America/Los_Angeles',
  'America/Louisville',
  'America/Lower_Princes',
  'America/Maceio',
  'America/Managua',
  'America/Manaus',
  'America/Marigot',
  'America/Martinique',
  'America/Matamoros',
  'America/Mazatlan',
  'America/Mendoza',
  'America/Menominee',
  'America/Merida',
  'America/Metlakatla',
  'America/Mexico_City',
  'America/Miquelon',
  'America/Moncton',
  'America/Monterrey',
  'America/Montevideo',
  'America/Montserrat',
  'America/Nassau',
  'America/New_York',
  'America/Nome',
  'America/Noronha',
  'America/North_Dakota/Beulah',
  'America/North_Dakota/Center',
  'America/North_Dakota/New_Salem',
  'America/Ojinaga',
  'America/Panama',
  'America/Paramaribo',
  'America/Phoenix',
  'America/Port-au-Prince',
  'America/Port_of_Spain',
  'America/Porto_Velho',
  'America/Puerto_Rico',
  'America/Punta_Arenas',
  'America/Rankin_Inlet',
  'America/Recife',
  'America/Regina',
  'America/Resolute',
  'America/Rio_Branco',
  'America/Santarem',
  'America/Santiago',
  'America/Santo_Domingo',
  'America/Sao_Paulo',
  'America/Scoresbysund',
  'America/Sitka',
  'America/St_Barthelemy',
  'America/St_Johns',
  'America/St_Kitts',
  'America/St_Lucia',
  'America/St_Thomas',
  'America/St_Vincent',
  'America/Swift_Current',
  'America/Tegucigalpa',
  'America/Thule',
  'America/Tijuana',
  'America/Toronto',
  'America/Tortola',
  'America/Vancouver',
  'America/Whitehorse',
  'America/Winnipeg',
  'America/Yakutat',
  'Antarctica/Casey',
  'Antarctica/Davis',
  'Antarctica/DumontDUrville',
  'Antarctica/Macquarie',
  'Antarctica/Mawson',
  'Antarctica/McMurdo',
  'Antarctica/Palmer',
  'Antarctica/Rothera',
  'Antarctica/Syowa',
  'Antarctica/Troll',
  'Antarctica/Vostok',
  'Arctic/Longyearbyen',
  'Asia/Aden',
  'Asia/Almaty',
  'Asia/Amman',
  'Asia/Anadyr',
  'Asia/Aqtau',
  'Asia/Aqtobe',
  'Asia/Ashgabat',
  'Asia/Atyrau',
  'Asia/Baghdad',
  'Asia/Bahrain',
  'Asia/Baku',
  'Asia/Bangkok',
  'Asia/Barnaul',
  'Asia/Beirut',
  'Asia/Bishkek',
  'Asia/Brunei',
  'Asia/Calcutta',
  'Asia/Chita',
  'Asia/Colombo',
  'Asia/Damascus',
  'Asia/Dhaka',
  'Asia/Dili',
  'Asia/Dubai',
  'Asia/Dushanbe',
  'Asia/Famagusta',
  'Asia/Gaza',
  'Asia/Hebron',
  'Asia/Hong_Kong',
  'Asia/Hovd',
  'Asia/Irkutsk',
  'Asia/Jakarta',
  'Asia/Jayapura',
  'Asia/Jerusalem',
  'Asia/Kabul',
  'Asia/Kamchatka',
  'Asia/Karachi',
  'Asia/Katmandu',
  'Asia/Khandyga',
  'Asia/Krasnoyarsk',
  'Asia/Kuala_Lumpur',
  'Asia/Kuching',
  'Asia/Kuwait',
  'Asia/Macau',
  'Asia/Magadan',
  'Asia/Makassar',
  'Asia/Manila',
  'Asia/Muscat',
  'Asia/Nicosia',
  'Asia/Novokuznetsk',
  'Asia/Novosibirsk',
  'Asia/Omsk',
  'Asia/Oral',
  'Asia/Phnom_Penh',
  'Asia/Pontianak',
  'Asia/Pyongyang',
  'Asia/Qatar',
  'Asia/Qostanay',
  'Asia/Qyzylorda',
  'Asia/Rangoon',
  'Asia/Riyadh',
  'Asia/Saigon',
  'Asia/Sakhalin',
  'Asia/Samarkand',
  'Asia/Seoul',
  'Asia/Shanghai',
  'Asia/Singapore',
  'Asia/Srednekolymsk',
  'Asia/Taipei',
  'Asia/Tashkent',
  'Asia/Tbilisi',
  'Asia/Tehran',
  'Asia/Thimphu',
  'Asia/Tokyo',
  'Asia/Tomsk',
  'Asia/Ulaanbaatar',
  'Asia/Urumqi',
  'Asia/Ust-Nera',
  'Asia/Vientiane',
  'Asia/Vladivostok',
  'Asia/Yakutsk',
  'Asia/Yekaterinburg',
  'Asia/Yerevan',
  'Atlantic/Azores',
  'Atlantic/Bermuda',
  'Atlantic/Canary',
  'Atlantic/Cape_Verde',
  'Atlantic/Faeroe',
  'Atlantic/Madeira',
  'Atlantic/Reykjavik',
  'Atlantic/South_Georgia',
  'Atlantic/St_Helena',
  'Atlantic/Stanley',
  'Australia/Adelaide',
  'Australia/Brisbane',
  'Australia/Broken_Hill',
  'Australia/Darwin',
  'Australia/Eucla',
  'Australia/Hobart',
  'Australia/Lindeman',
  'Australia/Lord_Howe',
  'Australia/Melbourne',
  'Australia/Perth',
  'Australia/Sydney',
  'Europe/Amsterdam',
  'Europe/Andorra',
  'Europe/Astrakhan',
  'Europe/Athens',
  'Europe/Belgrade',
  'Europe/Berlin',
  'Europe/Bratislava',
  'Europe/Brussels',
  'Europe/Bucharest',
  'Europe/Budapest',
  'Europe/Busingen',
  'Europe/Chisinau',
  'Europe/Copenhagen',
  'Europe/Dublin',
  'Europe/Gibraltar',
  'Europe/Guernsey',
  'Europe/Helsinki',
  'Europe/Isle_of_Man',
  'Europe/Istanbul',
  'Europe/Jersey',
  'Europe/Kaliningrad',
  'Europe/Kiev',
  'Europe/Kirov',
  'Europe/Lisbon',
  'Europe/Ljubljana',
  'Europe/London',
  'Europe/Luxembourg',
  'Europe/Madrid',
  'Europe/Malta',
  'Europe/Mariehamn',
  'Europe/Minsk',
  'Europe/Monaco',
  'Europe/Moscow',
  'Europe/Oslo',
  'Europe/Paris',
  'Europe/Podgorica',
  'Europe/Prague',
  'Europe/Riga',
  'Europe/Rome',
  'Europe/Samara',
  'Europe/San_Marino',
  'Europe/Sarajevo',
  'Europe/Saratov',
  'Europe/Simferopol',
  'Europe/Skopje',
  'Europe/Sofia',
  'Europe/Stockholm',
  'Europe/Tallinn',
  'Europe/Tirane',
  'Europe/Ulyanovsk',
  'Europe/Vaduz',
  'Europe/Vatican',
  'Europe/Vienna',
  'Europe/Vilnius',
  'Europe/Volgograd',
  'Europe/Warsaw',
  'Europe/Zagreb',
  'Europe/Zurich',
  'Indian/Antananarivo',
  'Indian/Chagos',
  'Indian/Christmas',
  'Indian/Cocos',
  'Indian/Comoro',
  'Indian/Kerguelen',
  'Indian/Mahe',
  'Indian/Maldives',
  'Indian/Mauritius',
  'Indian/Mayotte',
  'Indian/Reunion',
  'Pacific/Apia',
  'Pacific/Auckland',
  'Pacific/Bougainville',
  'Pacific/Chatham',
  'Pacific/Easter',
  'Pacific/Efate',
  'Pacific/Enderbury',
  'Pacific/Fakaofo',
  'Pacific/Fiji',
  'Pacific/Funafuti',
  'Pacific/Galapagos',
  'Pacific/Gambier',
  'Pacific/Guadalcanal',
  'Pacific/Guam',
  'Pacific/Honolulu',
  'Pacific/Kiritimati',
  'Pacific/Kosrae',
  'Pacific/Kwajalein',
  'Pacific/Majuro',
  'Pacific/Marquesas',
  'Pacific/Midway',
  'Pacific/Nauru',
  'Pacific/Niue',
  'Pacific/Norfolk',
  'Pacific/Noumea',
  'Pacific/Pago_Pago',
  'Pacific/Palau',
  'Pacific/Pitcairn',
  'Pacific/Ponape',
  'Pacific/Port_Moresby',
  'Pacific/Rarotonga',
  'Pacific/Saipan',
  'Pacific/Tahiti',
  'Pacific/Tarawa',
  'Pacific/Tongatapu',
  'Pacific/Truk',
  'Pacific/Wake',
  'Pacific/Wallis',
])
export const enum_fan_notification_listners_type = pgEnum('enum_fan_notification_listners_type', [
  'eventPresale',
])
export const enum_festival_profiles_social_links_resource = pgEnum(
  'enum_festival_profiles_social_links_resource',
  [
    'Facebook',
    'Instagram',
    'TikTok',
    'SoundCloud',
    'Discord',
    'YouTube',
    'Twitter',
    'Twitch',
    'Pinterest',
    'Spotify',
    'BeatPort',
    'Website',
    'Dice',
    'TVMaze',
    'MusicBrainz',
    'Tunefind',
    'Line',
    'Genius',
    'Pandora',
    'Shazam',
    'Tidal',
    'LastFm',
    'Deezer',
    'Songkick',
    'Bandsintown',
    'Discogs',
    'Itunes',
    'Amazon',
  ],
)
export const enum_festival_profiles_origin = pgEnum('enum_festival_profiles_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_festivals_social_links_resource = pgEnum('enum_festivals_social_links_resource', [
  'Facebook',
  'Instagram',
  'TikTok',
  'SoundCloud',
  'Discord',
  'YouTube',
  'Twitter',
  'Twitch',
  'Pinterest',
  'Spotify',
  'BeatPort',
  'Website',
  'Dice',
  'TVMaze',
  'MusicBrainz',
  'Tunefind',
  'Line',
  'Genius',
  'Pandora',
  'Shazam',
  'Tidal',
  'LastFm',
  'Deezer',
  'Songkick',
  'Bandsintown',
  'Discogs',
  'Itunes',
  'Amazon',
])
export const enum_festivals_origin = pgEnum('enum_festivals_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_genres_type = pgEnum('enum_genres_type', [
  'Spotify',
  'GrayArea',
  'Dice',
  'ResidentAdvisor',
  'ChartMetric',
])
export const enum_hubs_type = pgEnum('enum_hubs_type', ['country', 'region', 'city'])
export const enum_orders_origin = pgEnum('enum_orders_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_pages_blocks_cta_links_link_type = pgEnum(
  'enum_pages_blocks_cta_links_link_type',
  ['reference', 'custom'],
)
export const enum_pages_blocks_cta_links_link_appearance = pgEnum(
  'enum_pages_blocks_cta_links_link_appearance',
  ['default', 'outline'],
)
export const enum_pages_blocks_content_columns_size = pgEnum(
  'enum_pages_blocks_content_columns_size',
  ['oneThird', 'half', 'twoThirds', 'full'],
)
export const enum_pages_blocks_content_columns_link_type = pgEnum(
  'enum_pages_blocks_content_columns_link_type',
  ['reference', 'custom'],
)
export const enum_pages_blocks_content_columns_link_appearance = pgEnum(
  'enum_pages_blocks_content_columns_link_appearance',
  ['default', 'outline'],
)
export const enum_pages_blocks_archive_populate_by = pgEnum(
  'enum_pages_blocks_archive_populate_by',
  ['collection', 'selection'],
)
export const enum_pages_blocks_archive_relation_to = pgEnum(
  'enum_pages_blocks_archive_relation_to',
  ['pages'],
)
export const enum_pages_status = pgEnum('enum_pages_status', ['draft', 'published'])
export const enum__pages_v_blocks_cta_links_link_type = pgEnum(
  'enum__pages_v_blocks_cta_links_link_type',
  ['reference', 'custom'],
)
export const enum__pages_v_blocks_cta_links_link_appearance = pgEnum(
  'enum__pages_v_blocks_cta_links_link_appearance',
  ['default', 'outline'],
)
export const enum__pages_v_blocks_content_columns_size = pgEnum(
  'enum__pages_v_blocks_content_columns_size',
  ['oneThird', 'half', 'twoThirds', 'full'],
)
export const enum__pages_v_blocks_content_columns_link_type = pgEnum(
  'enum__pages_v_blocks_content_columns_link_type',
  ['reference', 'custom'],
)
export const enum__pages_v_blocks_content_columns_link_appearance = pgEnum(
  'enum__pages_v_blocks_content_columns_link_appearance',
  ['default', 'outline'],
)
export const enum__pages_v_blocks_archive_populate_by = pgEnum(
  'enum__pages_v_blocks_archive_populate_by',
  ['collection', 'selection'],
)
export const enum__pages_v_blocks_archive_relation_to = pgEnum(
  'enum__pages_v_blocks_archive_relation_to',
  ['pages'],
)
export const enum__pages_v_version_status = pgEnum('enum__pages_v_version_status', [
  'draft',
  'published',
])
export const enum_residencies_social_links_resource = pgEnum(
  'enum_residencies_social_links_resource',
  [
    'Facebook',
    'Instagram',
    'TikTok',
    'SoundCloud',
    'Discord',
    'YouTube',
    'Twitter',
    'Twitch',
    'Pinterest',
    'Spotify',
    'BeatPort',
    'Website',
    'Dice',
    'TVMaze',
    'MusicBrainz',
    'Tunefind',
    'Line',
    'Genius',
    'Pandora',
    'Shazam',
    'Tidal',
    'LastFm',
    'Deezer',
    'Songkick',
    'Bandsintown',
    'Discogs',
    'Itunes',
    'Amazon',
  ],
)
export const enum_ticket_types_origin = pgEnum('enum_ticket_types_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_users_roles = pgEnum('enum_users_roles', ['super-admin', 'user', 'read-only'])
export const enum_users_tenants_roles = pgEnum('enum_users_tenants_roles', [
  'tenant-admin',
  'tenant-viewer',
])
export const enum_venues_internal_contacts_type = pgEnum('enum_venues_internal_contacts_type', [
  'Management',
  'Marketing',
  'Advancing',
  'Production',
  'Ticketing',
  'Finance',
])
export const enum_venues_social_links_resource = pgEnum('enum_venues_social_links_resource', [
  'Facebook',
  'Instagram',
  'TikTok',
  'SoundCloud',
  'Discord',
  'YouTube',
  'Twitter',
  'Twitch',
  'Pinterest',
  'Spotify',
  'BeatPort',
  'Website',
  'Dice',
  'TVMaze',
  'MusicBrainz',
  'Tunefind',
  'Line',
  'Genius',
  'Pandora',
  'Shazam',
  'Tidal',
  'LastFm',
  'Deezer',
  'Songkick',
  'Bandsintown',
  'Discogs',
  'Itunes',
  'Amazon',
])
export const enum_venues_origin = pgEnum('enum_venues_origin', [
  'sanity',
  'dice',
  'ra',
  'songkick',
  'eventbrite',
])
export const enum_redirects_to_type = pgEnum('enum_redirects_to_type', ['reference', 'custom'])
export const enum_payload_jobs_log_task_slug = pgEnum('enum_payload_jobs_log_task_slug', [
  'inline',
  'importDiceEvent',
  'extractEventUrls',
  'getSitemapUrls',
  'qflowLogin',
  'qflowFetchEvents',
  'qflowSyncAttendees',
  'fetchEventWithTicketTypes',
  'upsertTicketTypes',
  'fetchOrders',
  'upsertTickets',
  'getConcertSitemapUrls',
  'collectConcertUrls',
  'fetchHtmlAndSave',
  'saveRawSanityData',
  'importSanityData',
  'handleSongkickEvent',
  'extractKeysByBucket',
  'extractLdJson',
  'extractRetailerId',
  'findOrCreateEvent',
  'findOrCreateTicket',
  'createOrder',
  'importPlanetscaleUser',
  'importPlanetscalePresaleRegistration',
  'schedulePublish',
])
export const enum_payload_jobs_log_state = pgEnum('enum_payload_jobs_log_state', [
  'failed',
  'succeeded',
])
export const enum_payload_jobs_workflow_slug = pgEnum('enum_payload_jobs_workflow_slug', [
  'crawlSanity',
  'crawlRAWorkflow',
  'crawlSongkickWorkflow',
  'crawlDicekWorkflow',
  'importSanityDataWorkflow',
  'syncDicePartnerEventsWorkflow',
  'syncDiceOrdersWorkflow',
  'syncDiceWorkflow',
  'syncQFlowWorkflow',
  'syncSongkickWorkflow',
  'syncPlanetscaleUsersWorkflow',
  'syncPlanetscalePresaleRegistrationsWorkflow',
])
export const enum_payload_jobs_task_slug = pgEnum('enum_payload_jobs_task_slug', [
  'inline',
  'importDiceEvent',
  'extractEventUrls',
  'getSitemapUrls',
  'qflowLogin',
  'qflowFetchEvents',
  'qflowSyncAttendees',
  'fetchEventWithTicketTypes',
  'upsertTicketTypes',
  'fetchOrders',
  'upsertTickets',
  'getConcertSitemapUrls',
  'collectConcertUrls',
  'fetchHtmlAndSave',
  'saveRawSanityData',
  'importSanityData',
  'handleSongkickEvent',
  'extractKeysByBucket',
  'extractLdJson',
  'extractRetailerId',
  'findOrCreateEvent',
  'findOrCreateTicket',
  'createOrder',
  'importPlanetscaleUser',
  'importPlanetscalePresaleRegistration',
  'schedulePublish',
])

export const agencies = pgTable(
  'agencies',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    address: varchar('address'),
    generalContacInfo_email: varchar('general_contac_info_email').notNull(),
    generalContacInfo_phoneNumber: varchar('general_contac_info_phone_number').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    agencies_updated_at_idx: index('agencies_updated_at_idx').on(columns.updatedAt),
    agencies_created_at_idx: index('agencies_created_at_idx').on(columns.createdAt),
  }),
)

export const agents = pgTable(
  'agents',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    title: enum_agents_title('title').notNull().default('regular'),
    agency: integer('agency_id')
      .notNull()
      .references(() => agencies.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    agents_agency_idx: index('agents_agency_idx').on(columns.agency),
    agents_updated_at_idx: index('agents_updated_at_idx').on(columns.updatedAt),
    agents_created_at_idx: index('agents_created_at_idx').on(columns.createdAt),
  }),
)

export const agents_texts = pgTable(
  'agents_texts',
  {
    id: serial('id').primaryKey(),
    order: integer('order').notNull(),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    text: varchar('text'),
  },
  (columns) => ({
    orderParentIdx: index('agents_texts_order_parent_idx').on(columns.order, columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [agents.id],
      name: 'agents_texts_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const articles = pgTable(
  'articles',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    type: enum_articles_type('type').notNull(),
    title: varchar('title').notNull(),
    author: integer('author_id')
      .notNull()
      .references(() => authors.id, {
        onDelete: 'set null',
      }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    articles_author_idx: index('articles_author_idx').on(columns.author),
    articles_preview_image_idx: index('articles_preview_image_idx').on(columns.previewImage),
    articles_overview_overview_overview_overview_hero_idx: index(
      'articles_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    articles_meta_meta_meta_meta_image_idx: index('articles_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    articles_slug_idx: index('articles_slug_idx').on(columns.slug),
    articles_updated_at_idx: index('articles_updated_at_idx').on(columns.updatedAt),
    articles_created_at_idx: index('articles_created_at_idx').on(columns.createdAt),
  }),
)

export const articles_rels = pgTable(
  'articles_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    eventsID: integer('events_id'),
    artistsID: integer('artists_id'),
    eventBrandsID: integer('event_brands_id'),
    festivalsID: integer('festivals_id'),
    residenciesID: integer('residencies_id'),
    venuesID: integer('venues_id'),
    authorsID: integer('authors_id'),
    ochoEpisodesID: integer('ocho_episodes_id'),
    hubsID: integer('hubs_id'),
  },
  (columns) => ({
    order: index('articles_rels_order_idx').on(columns.order),
    parentIdx: index('articles_rels_parent_idx').on(columns.parent),
    pathIdx: index('articles_rels_path_idx').on(columns.path),
    articles_rels_events_id_idx: index('articles_rels_events_id_idx').on(columns.eventsID),
    articles_rels_artists_id_idx: index('articles_rels_artists_id_idx').on(columns.artistsID),
    articles_rels_event_brands_id_idx: index('articles_rels_event_brands_id_idx').on(
      columns.eventBrandsID,
    ),
    articles_rels_festivals_id_idx: index('articles_rels_festivals_id_idx').on(columns.festivalsID),
    articles_rels_residencies_id_idx: index('articles_rels_residencies_id_idx').on(
      columns.residenciesID,
    ),
    articles_rels_venues_id_idx: index('articles_rels_venues_id_idx').on(columns.venuesID),
    articles_rels_authors_id_idx: index('articles_rels_authors_id_idx').on(columns.authorsID),
    articles_rels_ocho_episodes_id_idx: index('articles_rels_ocho_episodes_id_idx').on(
      columns.ochoEpisodesID,
    ),
    articles_rels_hubs_id_idx: index('articles_rels_hubs_id_idx').on(columns.hubsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [articles.id],
      name: 'articles_rels_parent_fk',
    }).onDelete('cascade'),
    eventsIdFk: foreignKey({
      columns: [columns['eventsID']],
      foreignColumns: [events.id],
      name: 'articles_rels_events_fk',
    }).onDelete('cascade'),
    artistsIdFk: foreignKey({
      columns: [columns['artistsID']],
      foreignColumns: [artists.id],
      name: 'articles_rels_artists_fk',
    }).onDelete('cascade'),
    eventBrandsIdFk: foreignKey({
      columns: [columns['eventBrandsID']],
      foreignColumns: [event_brands.id],
      name: 'articles_rels_event_brands_fk',
    }).onDelete('cascade'),
    festivalsIdFk: foreignKey({
      columns: [columns['festivalsID']],
      foreignColumns: [festivals.id],
      name: 'articles_rels_festivals_fk',
    }).onDelete('cascade'),
    residenciesIdFk: foreignKey({
      columns: [columns['residenciesID']],
      foreignColumns: [residencies.id],
      name: 'articles_rels_residencies_fk',
    }).onDelete('cascade'),
    venuesIdFk: foreignKey({
      columns: [columns['venuesID']],
      foreignColumns: [venues.id],
      name: 'articles_rels_venues_fk',
    }).onDelete('cascade'),
    authorsIdFk: foreignKey({
      columns: [columns['authorsID']],
      foreignColumns: [authors.id],
      name: 'articles_rels_authors_fk',
    }).onDelete('cascade'),
    ochoEpisodesIdFk: foreignKey({
      columns: [columns['ochoEpisodesID']],
      foreignColumns: [ocho_episodes.id],
      name: 'articles_rels_ocho_episodes_fk',
    }).onDelete('cascade'),
    hubsIdFk: foreignKey({
      columns: [columns['hubsID']],
      foreignColumns: [hubs.id],
      name: 'articles_rels_hubs_fk',
    }).onDelete('cascade'),
  }),
)

export const artist_deals_status_history = pgTable(
  'artist_deals_status_history',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    status: enum_artist_deals_status_history_status('status'),
    description: varchar('description'),
  },
  (columns) => ({
    _orderIdx: index('artist_deals_status_history_order_idx').on(columns._order),
    _parentIDIdx: index('artist_deals_status_history_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [artist_deals.id],
      name: 'artist_deals_status_history_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const artist_deals = pgTable(
  'artist_deals',
  {
    id: serial('id').primaryKey(),
    artist: integer('artist_id')
      .notNull()
      .references(() => artists.id, {
        onDelete: 'set null',
      }),
    event: integer('event_id')
      .notNull()
      .references(() => events.id, {
        onDelete: 'set null',
      }),
    expenses: numeric('expenses').notNull().default('0'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    artist_deals_artist_idx: index('artist_deals_artist_idx').on(columns.artist),
    artist_deals_event_idx: index('artist_deals_event_idx').on(columns.event),
    artist_deals_updated_at_idx: index('artist_deals_updated_at_idx').on(columns.updatedAt),
    artist_deals_created_at_idx: index('artist_deals_created_at_idx').on(columns.createdAt),
  }),
)

export const artist_deals_rels = pgTable(
  'artist_deals_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    documentsID: integer('documents_id'),
  },
  (columns) => ({
    order: index('artist_deals_rels_order_idx').on(columns.order),
    parentIdx: index('artist_deals_rels_parent_idx').on(columns.parent),
    pathIdx: index('artist_deals_rels_path_idx').on(columns.path),
    artist_deals_rels_documents_id_idx: index('artist_deals_rels_documents_id_idx').on(
      columns.documentsID,
    ),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [artist_deals.id],
      name: 'artist_deals_rels_parent_fk',
    }).onDelete('cascade'),
    documentsIdFk: foreignKey({
      columns: [columns['documentsID']],
      foreignColumns: [documents.id],
      name: 'artist_deals_rels_documents_fk',
    }).onDelete('cascade'),
  }),
)

export const artists_agency_territory_representations = pgTable(
  'artists_agency_territory_representations',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    global: boolean('global').default(false),
    agency: integer('agency_id')
      .notNull()
      .references(() => agencies.id, {
        onDelete: 'set null',
      }),
    verified: boolean('verified').default(true),
  },
  (columns) => ({
    _orderIdx: index('artists_agency_territory_representations_order_idx').on(columns._order),
    _parentIDIdx: index('artists_agency_territory_representations_parent_id_idx').on(
      columns._parentID,
    ),
    artists_agency_territory_representations_agency_idx: index(
      'artists_agency_territory_representations_agency_idx',
    ).on(columns.agency),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [artists.id],
      name: 'artists_agency_territory_representations_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const artists_management_representations = pgTable(
  'artists_management_representations',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
  },
  (columns) => ({
    _orderIdx: index('artists_management_representations_order_idx').on(columns._order),
    _parentIDIdx: index('artists_management_representations_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [artists.id],
      name: 'artists_management_representations_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const artists_social_links = pgTable(
  'artists_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_artists_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('artists_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('artists_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [artists.id],
      name: 'artists_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const artists = pgTable(
  'artists',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    spotifyId: varchar('spotify_id').notNull(),
    chartMetricExternalId: varchar('chart_metric_external_id'),
    externalSanityId: varchar('external_sanity_id'),
    country: integer('country_id').references(() => countries.id, {
      onDelete: 'set null',
    }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    artists_country_idx: index('artists_country_idx').on(columns.country),
    artists_preview_image_idx: index('artists_preview_image_idx').on(columns.previewImage),
    artists_overview_overview_overview_overview_hero_idx: index(
      'artists_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    artists_meta_meta_meta_meta_image_idx: index('artists_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    artists_slug_idx: index('artists_slug_idx').on(columns.slug),
    artists_updated_at_idx: index('artists_updated_at_idx').on(columns.updatedAt),
    artists_created_at_idx: index('artists_created_at_idx').on(columns.createdAt),
  }),
)

export const artists_texts = pgTable(
  'artists_texts',
  {
    id: serial('id').primaryKey(),
    order: integer('order').notNull(),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    text: varchar('text'),
  },
  (columns) => ({
    orderParentIdx: index('artists_texts_order_parent_idx').on(columns.order, columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [artists.id],
      name: 'artists_texts_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const artists_rels = pgTable(
  'artists_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    genresID: integer('genres_id'),
    residenciesID: integer('residencies_id'),
    countriesID: integer('countries_id'),
    agentsID: integer('agents_id'),
    managersID: integer('managers_id'),
  },
  (columns) => ({
    order: index('artists_rels_order_idx').on(columns.order),
    parentIdx: index('artists_rels_parent_idx').on(columns.parent),
    pathIdx: index('artists_rels_path_idx').on(columns.path),
    artists_rels_genres_id_idx: index('artists_rels_genres_id_idx').on(columns.genresID),
    artists_rels_residencies_id_idx: index('artists_rels_residencies_id_idx').on(
      columns.residenciesID,
    ),
    artists_rels_countries_id_idx: index('artists_rels_countries_id_idx').on(columns.countriesID),
    artists_rels_agents_id_idx: index('artists_rels_agents_id_idx').on(columns.agentsID),
    artists_rels_managers_id_idx: index('artists_rels_managers_id_idx').on(columns.managersID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [artists.id],
      name: 'artists_rels_parent_fk',
    }).onDelete('cascade'),
    genresIdFk: foreignKey({
      columns: [columns['genresID']],
      foreignColumns: [genres.id],
      name: 'artists_rels_genres_fk',
    }).onDelete('cascade'),
    residenciesIdFk: foreignKey({
      columns: [columns['residenciesID']],
      foreignColumns: [residencies.id],
      name: 'artists_rels_residencies_fk',
    }).onDelete('cascade'),
    countriesIdFk: foreignKey({
      columns: [columns['countriesID']],
      foreignColumns: [countries.id],
      name: 'artists_rels_countries_fk',
    }).onDelete('cascade'),
    agentsIdFk: foreignKey({
      columns: [columns['agentsID']],
      foreignColumns: [agents.id],
      name: 'artists_rels_agents_fk',
    }).onDelete('cascade'),
    managersIdFk: foreignKey({
      columns: [columns['managersID']],
      foreignColumns: [managers.id],
      name: 'artists_rels_managers_fk',
    }).onDelete('cascade'),
  }),
)

export const authors_social_links = pgTable(
  'authors_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_authors_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('authors_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('authors_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [authors.id],
      name: 'authors_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const authors = pgTable(
  'authors',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    country: integer('country_id').references(() => countries.id, {
      onDelete: 'set null',
    }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    authors_country_idx: index('authors_country_idx').on(columns.country),
    authors_preview_image_idx: index('authors_preview_image_idx').on(columns.previewImage),
    authors_overview_overview_overview_overview_hero_idx: index(
      'authors_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    authors_meta_meta_meta_meta_image_idx: index('authors_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    authors_slug_idx: index('authors_slug_idx').on(columns.slug),
    authors_updated_at_idx: index('authors_updated_at_idx').on(columns.updatedAt),
    authors_created_at_idx: index('authors_created_at_idx').on(columns.createdAt),
  }),
)

export const countries = pgTable(
  'countries',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    code: varchar('code').notNull(),
    flag: integer('flag_id')
      .notNull()
      .references(() => media.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    countries_flag_idx: index('countries_flag_idx').on(columns.flag),
    countries_updated_at_idx: index('countries_updated_at_idx').on(columns.updatedAt),
    countries_created_at_idx: index('countries_created_at_idx').on(columns.createdAt),
  }),
)

export const documents = pgTable(
  'documents',
  {
    id: serial('id').primaryKey(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    url: varchar('url'),
    thumbnailURL: varchar('thumbnail_u_r_l'),
    filename: varchar('filename'),
    mimeType: varchar('mime_type'),
    filesize: numeric('filesize'),
    width: numeric('width'),
    height: numeric('height'),
    focalX: numeric('focal_x'),
    focalY: numeric('focal_y'),
  },
  (columns) => ({
    documents_updated_at_idx: index('documents_updated_at_idx').on(columns.updatedAt),
    documents_created_at_idx: index('documents_created_at_idx').on(columns.createdAt),
    documents_filename_idx: uniqueIndex('documents_filename_idx').on(columns.filename),
  }),
)

export const event_brands_social_links = pgTable(
  'event_brands_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_event_brands_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('event_brands_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('event_brands_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [event_brands.id],
      name: 'event_brands_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const event_brands = pgTable(
  'event_brands',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    country: integer('country_id').references(() => countries.id, {
      onDelete: 'set null',
    }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    event_brands_country_idx: index('event_brands_country_idx').on(columns.country),
    event_brands_preview_image_idx: index('event_brands_preview_image_idx').on(
      columns.previewImage,
    ),
    event_brands_overview_overview_overview_overview_hero_idx: index(
      'event_brands_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    event_brands_meta_meta_meta_meta_image_idx: index(
      'event_brands_meta_meta_meta_meta_image_idx',
    ).on(columns.meta_meta_image),
    event_brands_slug_idx: index('event_brands_slug_idx').on(columns.slug),
    event_brands_updated_at_idx: index('event_brands_updated_at_idx').on(columns.updatedAt),
    event_brands_created_at_idx: index('event_brands_created_at_idx').on(columns.createdAt),
  }),
)

export const event_organizers = pgTable(
  'event_organizers',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    presentedBy: varchar('presented_by'),
    diceCredentials_login: varchar('dice_credentials_login'),
    diceCredentials_password: varchar('dice_credentials_password'),
    diceCredentials_DICE_PARTNER_API_TOKEN: varchar('dice_credentials_dice_partner_api_token'),
    residentAdvisorCredentials_login: varchar('resident_advisor_credentials_login'),
    residentAdvisorCredentials_password: varchar('resident_advisor_credentials_password'),
    qflowCredentials_login: varchar('qflow_credentials_login'),
    qflowCredentials_password: varchar('qflow_credentials_password'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    event_organizers_overview_overview_overview_overview_hero_idx: index(
      'event_organizers_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    event_organizers_meta_meta_meta_meta_image_idx: index(
      'event_organizers_meta_meta_meta_meta_image_idx',
    ).on(columns.meta_meta_image),
    event_organizers_slug_idx: index('event_organizers_slug_idx').on(columns.slug),
    event_organizers_updated_at_idx: index('event_organizers_updated_at_idx').on(columns.updatedAt),
    event_organizers_created_at_idx: index('event_organizers_created_at_idx').on(columns.createdAt),
  }),
)

export const events_external_platform_source_urls = pgTable(
  'events_external_platform_source_urls',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    platform: enum_events_external_platform_source_urls_platform('platform').notNull(),
    sourceUrl: varchar('source_url').notNull(),
  },
  (columns) => ({
    _orderIdx: index('events_external_platform_source_urls_order_idx').on(columns._order),
    _parentIDIdx: index('events_external_platform_source_urls_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_external_platform_source_urls_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events_lineup = pgTable(
  'events_lineup',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    artist: integer('artist_id')
      .notNull()
      .references(() => artists.id, {
        onDelete: 'set null',
      }),
    tier: enum_events_lineup_tier('tier'),
    startTime: timestamp('start_time', { mode: 'string', withTimezone: true, precision: 3 }),
  },
  (columns) => ({
    _orderIdx: index('events_lineup_order_idx').on(columns._order),
    _parentIDIdx: index('events_lineup_parent_id_idx').on(columns._parentID),
    events_lineup_artist_idx: index('events_lineup_artist_idx').on(columns.artist),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_lineup_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events_social_links = pgTable(
  'events_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_events_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('events_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('events_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events_faqs = pgTable(
  'events_faqs',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    question: varchar('question').notNull(),
    answer: jsonb('answer').notNull(),
  },
  (columns) => ({
    _orderIdx: index('events_faqs_order_idx').on(columns._order),
    _parentIDIdx: index('events_faqs_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_faqs_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events_ticket_types = pgTable(
  'events_ticket_types',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    ticketType: integer('ticket_type_id')
      .notNull()
      .references(() => ticket_types.id, {
        onDelete: 'set null',
      }),
  },
  (columns) => ({
    _orderIdx: index('events_ticket_types_order_idx').on(columns._order),
    _parentIDIdx: index('events_ticket_types_parent_id_idx').on(columns._parentID),
    events_ticket_types_ticket_type_idx: index('events_ticket_types_ticket_type_idx').on(
      columns.ticketType,
    ),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_ticket_types_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events_marketing_tracking_links = pgTable(
  'events_marketing_tracking_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    platform: enum_events_marketing_tracking_links_platform('platform').default('dice'),
    channel: varchar('channel').notNull(),
    dealsParams: enum_events_marketing_tracking_links_deals_params('deals_params'),
    campaign: varchar('campaign').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('events_marketing_tracking_links_order_idx').on(columns._order),
    _parentIDIdx: index('events_marketing_tracking_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [events.id],
      name: 'events_marketing_tracking_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const events = pgTable(
  'events',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    eventOrganizer: integer('event_organizer_id').references(() => event_organizers.id, {
      onDelete: 'set null',
    }),
    origin: enum_events_origin('origin'),
    externalSanityId: varchar('external_sanity_id'),
    externalDiceId: varchar('external_dice_id'),
    externalResidentAdvisorId: varchar('external_resident_advisor_id'),
    externalEventBriteId: varchar('external_event_brite_id'),
    festival: integer('festival_id').references(() => festivals.id, {
      onDelete: 'set null',
    }),
    eventBrand: integer('event_brand_id').references(() => event_brands.id, {
      onDelete: 'set null',
    }),
    residency: integer('residency_id').references(() => residencies.id, {
      onDelete: 'set null',
    }),
    Location_locationType:
      enum_events_location_location_type('location_location_type').default('venue'),
    Location_venue: integer('location_venue_id').references(() => venues.id, {
      onDelete: 'set null',
    }),
    Location_hub: integer('location_hub_id').references(() => hubs.id, {
      onDelete: 'set null',
    }),
    timezone: enum_events_timezone('timezone'),
    announcementDate: timestamp('announcement_date', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    saleOnDate: timestamp('sale_on_date', { mode: 'string', withTimezone: true, precision: 3 }),
    saleOffDate: timestamp('sale_off_date', { mode: 'string', withTimezone: true, precision: 3 }),
    startDate: timestamp('start_date', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    endDate: timestamp('end_date', { mode: 'string', withTimezone: true, precision: 3 }).notNull(),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    minAge: numeric('min_age'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    events_event_organizer_idx: index('events_event_organizer_idx').on(columns.eventOrganizer),
    events_festival_idx: index('events_festival_idx').on(columns.festival),
    events_event_brand_idx: index('events_event_brand_idx').on(columns.eventBrand),
    events_residency_idx: index('events_residency_idx').on(columns.residency),
    events_location_location_venue_idx: index('events_location_location_venue_idx').on(
      columns.Location_venue,
    ),
    events_location_location_hub_idx: index('events_location_location_hub_idx').on(
      columns.Location_hub,
    ),
    events_preview_image_idx: index('events_preview_image_idx').on(columns.previewImage),
    events_overview_overview_overview_overview_hero_idx: index(
      'events_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    events_meta_meta_meta_meta_image_idx: index('events_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    events_slug_idx: index('events_slug_idx').on(columns.slug),
    events_updated_at_idx: index('events_updated_at_idx').on(columns.updatedAt),
    events_created_at_idx: index('events_created_at_idx').on(columns.createdAt),
  }),
)

export const events_texts = pgTable(
  'events_texts',
  {
    id: serial('id').primaryKey(),
    order: integer('order').notNull(),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    text: varchar('text'),
  },
  (columns) => ({
    orderParentIdx: index('events_texts_order_parent_idx').on(columns.order, columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [events.id],
      name: 'events_texts_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const events_rels = pgTable(
  'events_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    genresID: integer('genres_id'),
    eventsID: integer('events_id'),
    promotionsID: integer('promotions_id'),
  },
  (columns) => ({
    order: index('events_rels_order_idx').on(columns.order),
    parentIdx: index('events_rels_parent_idx').on(columns.parent),
    pathIdx: index('events_rels_path_idx').on(columns.path),
    events_rels_genres_id_idx: index('events_rels_genres_id_idx').on(columns.genresID),
    events_rels_events_id_idx: index('events_rels_events_id_idx').on(columns.eventsID),
    events_rels_promotions_id_idx: index('events_rels_promotions_id_idx').on(columns.promotionsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [events.id],
      name: 'events_rels_parent_fk',
    }).onDelete('cascade'),
    genresIdFk: foreignKey({
      columns: [columns['genresID']],
      foreignColumns: [genres.id],
      name: 'events_rels_genres_fk',
    }).onDelete('cascade'),
    eventsIdFk: foreignKey({
      columns: [columns['eventsID']],
      foreignColumns: [events.id],
      name: 'events_rels_events_fk',
    }).onDelete('cascade'),
    promotionsIdFk: foreignKey({
      columns: [columns['promotionsID']],
      foreignColumns: [promotions.id],
      name: 'events_rels_promotions_fk',
    }).onDelete('cascade'),
  }),
)

export const fan_notification_listners = pgTable(
  'fan_notification_listners',
  {
    id: serial('id').primaryKey(),
    type: enum_fan_notification_listners_type('type').notNull().default('eventPresale'),
    event: integer('event_id')
      .notNull()
      .references(() => events.id, {
        onDelete: 'set null',
      }),
    fan: integer('fan_id')
      .notNull()
      .references(() => fan_users.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    fan_notification_listners_event_idx: index('fan_notification_listners_event_idx').on(
      columns.event,
    ),
    fan_notification_listners_fan_idx: index('fan_notification_listners_fan_idx').on(columns.fan),
    fan_notification_listners_updated_at_idx: index('fan_notification_listners_updated_at_idx').on(
      columns.updatedAt,
    ),
    fan_notification_listners_created_at_idx: index('fan_notification_listners_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const fan_users = pgTable(
  'fan_users',
  {
    id: serial('id').primaryKey(),
    firstName: varchar('first_name').notNull(),
    lastName: varchar('last_name').notNull(),
    email: varchar('email').notNull(),
    phoneNumber: varchar('phone_number'),
    phoneNumberCountryCode: varchar('phone_number_country_code'),
    dateOfBirth: timestamp('date_of_birth', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    ethWallet: varchar('eth_wallet'),
    diceId: varchar('dice_id'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    fan_users_updated_at_idx: index('fan_users_updated_at_idx').on(columns.updatedAt),
    fan_users_created_at_idx: index('fan_users_created_at_idx').on(columns.createdAt),
  }),
)

export const fan_users_rels = pgTable(
  'fan_users_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    eventBrandsID: integer('event_brands_id'),
    genresID: integer('genres_id'),
    artistsID: integer('artists_id'),
    authorsID: integer('authors_id'),
    ordersID: integer('orders_id'),
  },
  (columns) => ({
    order: index('fan_users_rels_order_idx').on(columns.order),
    parentIdx: index('fan_users_rels_parent_idx').on(columns.parent),
    pathIdx: index('fan_users_rels_path_idx').on(columns.path),
    fan_users_rels_event_brands_id_idx: index('fan_users_rels_event_brands_id_idx').on(
      columns.eventBrandsID,
    ),
    fan_users_rels_genres_id_idx: index('fan_users_rels_genres_id_idx').on(columns.genresID),
    fan_users_rels_artists_id_idx: index('fan_users_rels_artists_id_idx').on(columns.artistsID),
    fan_users_rels_authors_id_idx: index('fan_users_rels_authors_id_idx').on(columns.authorsID),
    fan_users_rels_orders_id_idx: index('fan_users_rels_orders_id_idx').on(columns.ordersID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [fan_users.id],
      name: 'fan_users_rels_parent_fk',
    }).onDelete('cascade'),
    eventBrandsIdFk: foreignKey({
      columns: [columns['eventBrandsID']],
      foreignColumns: [event_brands.id],
      name: 'fan_users_rels_event_brands_fk',
    }).onDelete('cascade'),
    genresIdFk: foreignKey({
      columns: [columns['genresID']],
      foreignColumns: [genres.id],
      name: 'fan_users_rels_genres_fk',
    }).onDelete('cascade'),
    artistsIdFk: foreignKey({
      columns: [columns['artistsID']],
      foreignColumns: [artists.id],
      name: 'fan_users_rels_artists_fk',
    }).onDelete('cascade'),
    authorsIdFk: foreignKey({
      columns: [columns['authorsID']],
      foreignColumns: [authors.id],
      name: 'fan_users_rels_authors_fk',
    }).onDelete('cascade'),
    ordersIdFk: foreignKey({
      columns: [columns['ordersID']],
      foreignColumns: [orders.id],
      name: 'fan_users_rels_orders_fk',
    }).onDelete('cascade'),
  }),
)

export const festival_profiles_social_links = pgTable(
  'festival_profiles_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_festival_profiles_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('festival_profiles_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('festival_profiles_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [festival_profiles.id],
      name: 'festival_profiles_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const festival_profiles = pgTable(
  'festival_profiles',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    externalSanityId: varchar('external_sanity_id'),
    origin: enum_festival_profiles_origin('origin'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    festival_profiles_preview_image_idx: index('festival_profiles_preview_image_idx').on(
      columns.previewImage,
    ),
    festival_profiles_overview_overview_overview_overview_hero_idx: index(
      'festival_profiles_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    festival_profiles_meta_meta_meta_meta_image_idx: index(
      'festival_profiles_meta_meta_meta_meta_image_idx',
    ).on(columns.meta_meta_image),
    festival_profiles_slug_idx: index('festival_profiles_slug_idx').on(columns.slug),
    festival_profiles_updated_at_idx: index('festival_profiles_updated_at_idx').on(
      columns.updatedAt,
    ),
    festival_profiles_created_at_idx: index('festival_profiles_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const festivals_social_links = pgTable(
  'festivals_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_festivals_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('festivals_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('festivals_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [festivals.id],
      name: 'festivals_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const festivals = pgTable(
  'festivals',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    festivalBrand: integer('festival_brand_id').references(() => festival_profiles.id, {
      onDelete: 'set null',
    }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    externalSanityId: varchar('external_sanity_id'),
    origin: enum_festivals_origin('origin'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    festivals_festival_brand_idx: index('festivals_festival_brand_idx').on(columns.festivalBrand),
    festivals_preview_image_idx: index('festivals_preview_image_idx').on(columns.previewImage),
    festivals_overview_overview_overview_overview_hero_idx: index(
      'festivals_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    festivals_meta_meta_meta_meta_image_idx: index('festivals_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    festivals_slug_idx: index('festivals_slug_idx').on(columns.slug),
    festivals_updated_at_idx: index('festivals_updated_at_idx').on(columns.updatedAt),
    festivals_created_at_idx: index('festivals_created_at_idx').on(columns.createdAt),
  }),
)

export const genres = pgTable(
  'genres',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    type: enum_genres_type('type'),
    description: jsonb('description'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    fullTitle: varchar('full_title'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    genres_overview_overview_overview_overview_hero_idx: index(
      'genres_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    genres_meta_meta_meta_meta_image_idx: index('genres_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    genres_slug_idx: index('genres_slug_idx').on(columns.slug),
    genres_updated_at_idx: index('genres_updated_at_idx').on(columns.updatedAt),
    genres_created_at_idx: index('genres_created_at_idx').on(columns.createdAt),
  }),
)

export const hubs = pgTable(
  'hubs',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    formatted: varchar('formatted').notNull(),
    type: enum_hubs_type('type').default('city'),
    parent: integer('parent_id').references((): AnyPgColumn => hubs.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    hubs_parent_idx: index('hubs_parent_idx').on(columns.parent),
    hubs_overview_overview_overview_overview_hero_idx: index(
      'hubs_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    hubs_meta_meta_meta_meta_image_idx: index('hubs_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    hubs_updated_at_idx: index('hubs_updated_at_idx').on(columns.updatedAt),
    hubs_created_at_idx: index('hubs_created_at_idx').on(columns.createdAt),
  }),
)

export const managers = pgTable(
  'managers',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    title: varchar('title').notNull(),
    generalContacInfo_email: varchar('general_contac_info_email').notNull(),
    generalContacInfo_phoneNumber: varchar('general_contac_info_phone_number').notNull(),
    managmentCompany: integer('managment_company_id')
      .notNull()
      .references(() => managment_companies.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    managers_managment_company_idx: index('managers_managment_company_idx').on(
      columns.managmentCompany,
    ),
    managers_updated_at_idx: index('managers_updated_at_idx').on(columns.updatedAt),
    managers_created_at_idx: index('managers_created_at_idx').on(columns.createdAt),
  }),
)

export const managment_companies = pgTable(
  'managment_companies',
  {
    id: serial('id').primaryKey(),
    name: varchar('name').notNull(),
    address: varchar('address').notNull(),
    generalContacInfo_email: varchar('general_contac_info_email').notNull(),
    generalContacInfo_phoneNumber: varchar('general_contac_info_phone_number').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    managment_companies_updated_at_idx: index('managment_companies_updated_at_idx').on(
      columns.updatedAt,
    ),
    managment_companies_created_at_idx: index('managment_companies_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const media = pgTable(
  'media',
  {
    id: serial('id').primaryKey(),
    alt: varchar('alt'),
    caption: jsonb('caption'),
    prefix: varchar('prefix').default('media'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    url: varchar('url'),
    thumbnailURL: varchar('thumbnail_u_r_l'),
    filename: varchar('filename'),
    mimeType: varchar('mime_type'),
    filesize: numeric('filesize'),
    width: numeric('width'),
    height: numeric('height'),
    focalX: numeric('focal_x'),
    focalY: numeric('focal_y'),
    sizes_thumbnail_url: varchar('sizes_thumbnail_url'),
    sizes_thumbnail_width: numeric('sizes_thumbnail_width'),
    sizes_thumbnail_height: numeric('sizes_thumbnail_height'),
    sizes_thumbnail_mimeType: varchar('sizes_thumbnail_mime_type'),
    sizes_thumbnail_filesize: numeric('sizes_thumbnail_filesize'),
    sizes_thumbnail_filename: varchar('sizes_thumbnail_filename'),
    sizes_square_url: varchar('sizes_square_url'),
    sizes_square_width: numeric('sizes_square_width'),
    sizes_square_height: numeric('sizes_square_height'),
    sizes_square_mimeType: varchar('sizes_square_mime_type'),
    sizes_square_filesize: numeric('sizes_square_filesize'),
    sizes_square_filename: varchar('sizes_square_filename'),
    sizes_small_url: varchar('sizes_small_url'),
    sizes_small_width: numeric('sizes_small_width'),
    sizes_small_height: numeric('sizes_small_height'),
    sizes_small_mimeType: varchar('sizes_small_mime_type'),
    sizes_small_filesize: numeric('sizes_small_filesize'),
    sizes_small_filename: varchar('sizes_small_filename'),
    sizes_medium_url: varchar('sizes_medium_url'),
    sizes_medium_width: numeric('sizes_medium_width'),
    sizes_medium_height: numeric('sizes_medium_height'),
    sizes_medium_mimeType: varchar('sizes_medium_mime_type'),
    sizes_medium_filesize: numeric('sizes_medium_filesize'),
    sizes_medium_filename: varchar('sizes_medium_filename'),
    sizes_large_url: varchar('sizes_large_url'),
    sizes_large_width: numeric('sizes_large_width'),
    sizes_large_height: numeric('sizes_large_height'),
    sizes_large_mimeType: varchar('sizes_large_mime_type'),
    sizes_large_filesize: numeric('sizes_large_filesize'),
    sizes_large_filename: varchar('sizes_large_filename'),
    sizes_xlarge_url: varchar('sizes_xlarge_url'),
    sizes_xlarge_width: numeric('sizes_xlarge_width'),
    sizes_xlarge_height: numeric('sizes_xlarge_height'),
    sizes_xlarge_mimeType: varchar('sizes_xlarge_mime_type'),
    sizes_xlarge_filesize: numeric('sizes_xlarge_filesize'),
    sizes_xlarge_filename: varchar('sizes_xlarge_filename'),
    sizes_og_url: varchar('sizes_og_url'),
    sizes_og_width: numeric('sizes_og_width'),
    sizes_og_height: numeric('sizes_og_height'),
    sizes_og_mimeType: varchar('sizes_og_mime_type'),
    sizes_og_filesize: numeric('sizes_og_filesize'),
    sizes_og_filename: varchar('sizes_og_filename'),
  },
  (columns) => ({
    media_updated_at_idx: index('media_updated_at_idx').on(columns.updatedAt),
    media_created_at_idx: index('media_created_at_idx').on(columns.createdAt),
    media_filename_idx: uniqueIndex('media_filename_idx').on(columns.filename),
    media_sizes_thumbnail_sizes_thumbnail_filename_idx: index(
      'media_sizes_thumbnail_sizes_thumbnail_filename_idx',
    ).on(columns.sizes_thumbnail_filename),
    media_sizes_square_sizes_square_filename_idx: index(
      'media_sizes_square_sizes_square_filename_idx',
    ).on(columns.sizes_square_filename),
    media_sizes_small_sizes_small_filename_idx: index(
      'media_sizes_small_sizes_small_filename_idx',
    ).on(columns.sizes_small_filename),
    media_sizes_medium_sizes_medium_filename_idx: index(
      'media_sizes_medium_sizes_medium_filename_idx',
    ).on(columns.sizes_medium_filename),
    media_sizes_large_sizes_large_filename_idx: index(
      'media_sizes_large_sizes_large_filename_idx',
    ).on(columns.sizes_large_filename),
    media_sizes_xlarge_sizes_xlarge_filename_idx: index(
      'media_sizes_xlarge_sizes_xlarge_filename_idx',
    ).on(columns.sizes_xlarge_filename),
    media_sizes_og_sizes_og_filename_idx: index('media_sizes_og_sizes_og_filename_idx').on(
      columns.sizes_og_filename,
    ),
  }),
)

export const ocho_episodes = pgTable(
  'ocho_episodes',
  {
    id: serial('id').primaryKey(),
    title: varchar('title').notNull(),
    episodeNumber: numeric('episode_number').notNull(),
    description: jsonb('description').notNull(),
    videoUrl: varchar('video_url'),
    podcastUrl: varchar('podcast_url'),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    ocho_episodes_preview_image_idx: index('ocho_episodes_preview_image_idx').on(
      columns.previewImage,
    ),
    ocho_episodes_overview_overview_overview_overview_hero_idx: index(
      'ocho_episodes_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    ocho_episodes_meta_meta_meta_meta_image_idx: index(
      'ocho_episodes_meta_meta_meta_meta_image_idx',
    ).on(columns.meta_meta_image),
    ocho_episodes_slug_idx: index('ocho_episodes_slug_idx').on(columns.slug),
    ocho_episodes_updated_at_idx: index('ocho_episodes_updated_at_idx').on(columns.updatedAt),
    ocho_episodes_created_at_idx: index('ocho_episodes_created_at_idx').on(columns.createdAt),
  }),
)

export const ocho_episodes_rels = pgTable(
  'ocho_episodes_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    artistsID: integer('artists_id'),
  },
  (columns) => ({
    order: index('ocho_episodes_rels_order_idx').on(columns.order),
    parentIdx: index('ocho_episodes_rels_parent_idx').on(columns.parent),
    pathIdx: index('ocho_episodes_rels_path_idx').on(columns.path),
    ocho_episodes_rels_artists_id_idx: index('ocho_episodes_rels_artists_id_idx').on(
      columns.artistsID,
    ),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [ocho_episodes.id],
      name: 'ocho_episodes_rels_parent_fk',
    }).onDelete('cascade'),
    artistsIdFk: foreignKey({
      columns: [columns['artistsID']],
      foreignColumns: [artists.id],
      name: 'ocho_episodes_rels_artists_fk',
    }).onDelete('cascade'),
  }),
)

export const orders = pgTable(
  'orders',
  {
    id: serial('id').primaryKey(),
    externalDiceId: varchar('external_dice_id').notNull(),
    origin: enum_orders_origin('origin'),
    externalResidentAdvisorId: varchar('external_resident_advisor_id'),
    externalEventBriteId: varchar('external_event_brite_id'),
    event: integer('event_id').references(() => events.id, {
      onDelete: 'set null',
    }),
    fanEmail: varchar('fan_email').notNull(),
    fan: integer('fan_id').references(() => fan_users.id, {
      onDelete: 'set null',
    }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    orders_event_idx: index('orders_event_idx').on(columns.event),
    orders_fan_idx: index('orders_fan_idx').on(columns.fan),
    orders_updated_at_idx: index('orders_updated_at_idx').on(columns.updatedAt),
    orders_created_at_idx: index('orders_created_at_idx').on(columns.createdAt),
  }),
)

export const orders_rels = pgTable(
  'orders_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    ticketsID: integer('tickets_id'),
  },
  (columns) => ({
    order: index('orders_rels_order_idx').on(columns.order),
    parentIdx: index('orders_rels_parent_idx').on(columns.parent),
    pathIdx: index('orders_rels_path_idx').on(columns.path),
    orders_rels_tickets_id_idx: index('orders_rels_tickets_id_idx').on(columns.ticketsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [orders.id],
      name: 'orders_rels_parent_fk',
    }).onDelete('cascade'),
    ticketsIdFk: foreignKey({
      columns: [columns['ticketsID']],
      foreignColumns: [tickets.id],
      name: 'orders_rels_tickets_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_cta_links = pgTable(
  'pages_blocks_cta_links',
  {
    _order: integer('_order').notNull(),
    _parentID: varchar('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    link_type: enum_pages_blocks_cta_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum_pages_blocks_cta_links_link_appearance('link_appearance').default('default'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_cta_links_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_cta_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages_blocks_cta.id],
      name: 'pages_blocks_cta_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_cta = pgTable(
  'pages_blocks_cta',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    richText: jsonb('rich_text'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_cta_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_cta_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_cta_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_cta_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_content_columns = pgTable(
  'pages_blocks_content_columns',
  {
    _order: integer('_order').notNull(),
    _parentID: varchar('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    size: enum_pages_blocks_content_columns_size('size').default('oneThird'),
    richText: jsonb('rich_text'),
    enableLink: boolean('enable_link'),
    link_type: enum_pages_blocks_content_columns_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum_pages_blocks_content_columns_link_appearance('link_appearance').default('default'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_content_columns_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_content_columns_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages_blocks_content.id],
      name: 'pages_blocks_content_columns_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_content = pgTable(
  'pages_blocks_content',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_content_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_content_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_content_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_content_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_media_block = pgTable(
  'pages_blocks_media_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    media: integer('media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_media_block_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_media_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_media_block_path_idx').on(columns._path),
    pages_blocks_media_block_media_idx: index('pages_blocks_media_block_media_idx').on(
      columns.media,
    ),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_media_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_archive = pgTable(
  'pages_blocks_archive',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    introContent: jsonb('intro_content'),
    populateBy: enum_pages_blocks_archive_populate_by('populate_by').default('collection'),
    relationTo: enum_pages_blocks_archive_relation_to('relation_to'),
    limit: numeric('limit').default('10'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_archive_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_archive_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_archive_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_archive_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_events_block = pgTable(
  'pages_blocks_events_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    heading: varchar('heading').default('Events'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_events_block_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_events_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_events_block_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_events_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages_blocks_home_events_section = pgTable(
  'pages_blocks_home_events_section',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: varchar('id').primaryKey(),
    title: varchar('title').default('Upcoming Gray Area Events'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('pages_blocks_home_events_section_order_idx').on(columns._order),
    _parentIDIdx: index('pages_blocks_home_events_section_parent_id_idx').on(columns._parentID),
    _pathIdx: index('pages_blocks_home_events_section_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [pages.id],
      name: 'pages_blocks_home_events_section_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const pages = pgTable(
  'pages',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    meta_title: varchar('meta_title'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_description: varchar('meta_description'),
    publishedAt: timestamp('published_at', { mode: 'string', withTimezone: true, precision: 3 }),
    slug: varchar('slug'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    _status: enum_pages_status('_status').default('draft'),
  },
  (columns) => ({
    pages_meta_meta_image_idx: index('pages_meta_meta_image_idx').on(columns.meta_image),
    pages_slug_idx: index('pages_slug_idx').on(columns.slug),
    pages_updated_at_idx: index('pages_updated_at_idx').on(columns.updatedAt),
    pages_created_at_idx: index('pages_created_at_idx').on(columns.createdAt),
    pages__status_idx: index('pages__status_idx').on(columns._status),
  }),
)

export const pages_rels = pgTable(
  'pages_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    eventsID: integer('events_id'),
  },
  (columns) => ({
    order: index('pages_rels_order_idx').on(columns.order),
    parentIdx: index('pages_rels_parent_idx').on(columns.parent),
    pathIdx: index('pages_rels_path_idx').on(columns.path),
    pages_rels_pages_id_idx: index('pages_rels_pages_id_idx').on(columns.pagesID),
    pages_rels_events_id_idx: index('pages_rels_events_id_idx').on(columns.eventsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [pages.id],
      name: 'pages_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'pages_rels_pages_fk',
    }).onDelete('cascade'),
    eventsIdFk: foreignKey({
      columns: [columns['eventsID']],
      foreignColumns: [events.id],
      name: 'pages_rels_events_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_cta_links = pgTable(
  '_pages_v_blocks_cta_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    link_type: enum__pages_v_blocks_cta_links_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum__pages_v_blocks_cta_links_link_appearance('link_appearance').default('default'),
    _uuid: varchar('_uuid'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_cta_links_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_cta_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v_blocks_cta.id],
      name: '_pages_v_blocks_cta_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_cta = pgTable(
  '_pages_v_blocks_cta',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    richText: jsonb('rich_text'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_cta_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_cta_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_cta_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_cta_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_content_columns = pgTable(
  '_pages_v_blocks_content_columns',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: serial('id').primaryKey(),
    size: enum__pages_v_blocks_content_columns_size('size').default('oneThird'),
    richText: jsonb('rich_text'),
    enableLink: boolean('enable_link'),
    link_type: enum__pages_v_blocks_content_columns_link_type('link_type').default('reference'),
    link_newTab: boolean('link_new_tab'),
    link_url: varchar('link_url'),
    link_label: varchar('link_label'),
    link_appearance:
      enum__pages_v_blocks_content_columns_link_appearance('link_appearance').default('default'),
    _uuid: varchar('_uuid'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_content_columns_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_content_columns_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v_blocks_content.id],
      name: '_pages_v_blocks_content_columns_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_content = pgTable(
  '_pages_v_blocks_content',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_content_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_content_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_content_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_content_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_media_block = pgTable(
  '_pages_v_blocks_media_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    media: integer('media_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_media_block_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_media_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_media_block_path_idx').on(columns._path),
    _pages_v_blocks_media_block_media_idx: index('_pages_v_blocks_media_block_media_idx').on(
      columns.media,
    ),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_media_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_archive = pgTable(
  '_pages_v_blocks_archive',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    introContent: jsonb('intro_content'),
    populateBy: enum__pages_v_blocks_archive_populate_by('populate_by').default('collection'),
    relationTo: enum__pages_v_blocks_archive_relation_to('relation_to'),
    limit: numeric('limit').default('10'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_archive_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_archive_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_archive_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_archive_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_events_block = pgTable(
  '_pages_v_blocks_events_block',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    heading: varchar('heading').default('Events'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_events_block_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_events_block_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_events_block_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_events_block_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v_blocks_home_events_section = pgTable(
  '_pages_v_blocks_home_events_section',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    _path: text('_path').notNull(),
    id: serial('id').primaryKey(),
    title: varchar('title').default('Upcoming Gray Area Events'),
    _uuid: varchar('_uuid'),
    blockName: varchar('block_name'),
  },
  (columns) => ({
    _orderIdx: index('_pages_v_blocks_home_events_section_order_idx').on(columns._order),
    _parentIDIdx: index('_pages_v_blocks_home_events_section_parent_id_idx').on(columns._parentID),
    _pathIdx: index('_pages_v_blocks_home_events_section_path_idx').on(columns._path),
    _parentIdFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_blocks_home_events_section_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const _pages_v = pgTable(
  '_pages_v',
  {
    id: serial('id').primaryKey(),
    parent: integer('parent_id').references(() => pages.id, {
      onDelete: 'set null',
    }),
    version_title: varchar('version_title'),
    version_meta_title: varchar('version_meta_title'),
    version_meta_image: integer('version_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    version_meta_description: varchar('version_meta_description'),
    version_publishedAt: timestamp('version_published_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_slug: varchar('version_slug'),
    version_updatedAt: timestamp('version_updated_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version_createdAt: timestamp('version_created_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    version__status: enum__pages_v_version_status('version__status').default('draft'),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    latest: boolean('latest'),
    autosave: boolean('autosave'),
  },
  (columns) => ({
    _pages_v_parent_idx: index('_pages_v_parent_idx').on(columns.parent),
    _pages_v_version_meta_version_meta_image_idx: index(
      '_pages_v_version_meta_version_meta_image_idx',
    ).on(columns.version_meta_image),
    _pages_v_version_version_slug_idx: index('_pages_v_version_version_slug_idx').on(
      columns.version_slug,
    ),
    _pages_v_version_version_updated_at_idx: index('_pages_v_version_version_updated_at_idx').on(
      columns.version_updatedAt,
    ),
    _pages_v_version_version_created_at_idx: index('_pages_v_version_version_created_at_idx').on(
      columns.version_createdAt,
    ),
    _pages_v_version_version__status_idx: index('_pages_v_version_version__status_idx').on(
      columns.version__status,
    ),
    _pages_v_created_at_idx: index('_pages_v_created_at_idx').on(columns.createdAt),
    _pages_v_updated_at_idx: index('_pages_v_updated_at_idx').on(columns.updatedAt),
    _pages_v_latest_idx: index('_pages_v_latest_idx').on(columns.latest),
    _pages_v_autosave_idx: index('_pages_v_autosave_idx').on(columns.autosave),
  }),
)

export const _pages_v_rels = pgTable(
  '_pages_v_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
    eventsID: integer('events_id'),
  },
  (columns) => ({
    order: index('_pages_v_rels_order_idx').on(columns.order),
    parentIdx: index('_pages_v_rels_parent_idx').on(columns.parent),
    pathIdx: index('_pages_v_rels_path_idx').on(columns.path),
    _pages_v_rels_pages_id_idx: index('_pages_v_rels_pages_id_idx').on(columns.pagesID),
    _pages_v_rels_events_id_idx: index('_pages_v_rels_events_id_idx').on(columns.eventsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [_pages_v.id],
      name: '_pages_v_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: '_pages_v_rels_pages_fk',
    }).onDelete('cascade'),
    eventsIdFk: foreignKey({
      columns: [columns['eventsID']],
      foreignColumns: [events.id],
      name: '_pages_v_rels_events_fk',
    }).onDelete('cascade'),
  }),
)

export const promotions_code_locks = pgTable(
  'promotions_code_locks',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    code: varchar('code').notNull(),
    claimedBy: integer('claimed_by_id').references(() => fan_users.id, {
      onDelete: 'set null',
    }),
  },
  (columns) => ({
    _orderIdx: index('promotions_code_locks_order_idx').on(columns._order),
    _parentIDIdx: index('promotions_code_locks_parent_id_idx').on(columns._parentID),
    promotions_code_locks_claimed_by_idx: index('promotions_code_locks_claimed_by_idx').on(
      columns.claimedBy,
    ),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [promotions.id],
      name: 'promotions_code_locks_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const promotions = pgTable(
  'promotions',
  {
    id: serial('id').primaryKey(),
    title: varchar('title').notNull(),
    startDate: timestamp('start_date', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    endDate: timestamp('end_date', { mode: 'string', withTimezone: true, precision: 3 }).notNull(),
    maxRedemptions: numeric('max_redemptions').notNull().default('0'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    promotions_updated_at_idx: index('promotions_updated_at_idx').on(columns.updatedAt),
    promotions_created_at_idx: index('promotions_created_at_idx').on(columns.createdAt),
  }),
)

export const residencies_social_links = pgTable(
  'residencies_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_residencies_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('residencies_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('residencies_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [residencies.id],
      name: 'residencies_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const residencies = pgTable(
  'residencies',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    eventBrand: integer('event_brand_id').references(() => event_brands.id, {
      onDelete: 'set null',
    }),
    Location_venue: integer('location_venue_id').references(() => venues.id, {
      onDelete: 'set null',
    }),
    Location_geoLocation_locationName: varchar('location_geo_location_location_name'),
    Location_geoLocation_link: varchar('location_geo_location_link'),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    residencies_event_brand_idx: index('residencies_event_brand_idx').on(columns.eventBrand),
    residencies_location_location_venue_idx: index('residencies_location_location_venue_idx').on(
      columns.Location_venue,
    ),
    residencies_preview_image_idx: index('residencies_preview_image_idx').on(columns.previewImage),
    residencies_overview_overview_overview_overview_hero_idx: index(
      'residencies_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    residencies_meta_meta_meta_meta_image_idx: index(
      'residencies_meta_meta_meta_meta_image_idx',
    ).on(columns.meta_meta_image),
    residencies_slug_idx: index('residencies_slug_idx').on(columns.slug),
    residencies_updated_at_idx: index('residencies_updated_at_idx').on(columns.updatedAt),
    residencies_created_at_idx: index('residencies_created_at_idx').on(columns.createdAt),
  }),
)

export const tickets = pgTable(
  'tickets',
  {
    id: serial('id').primaryKey(),
    externalDiceId: varchar('external_dice_id').notNull(),
    admittedDate: timestamp('admitted_date', { mode: 'string', withTimezone: true, precision: 3 }),
    code: varchar('code'),
    total: numeric('total'),
    ticketType: integer('ticket_type_id')
      .notNull()
      .references(() => ticket_types.id, {
        onDelete: 'set null',
      }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    tickets_ticket_type_idx: index('tickets_ticket_type_idx').on(columns.ticketType),
    tickets_updated_at_idx: index('tickets_updated_at_idx').on(columns.updatedAt),
    tickets_created_at_idx: index('tickets_created_at_idx').on(columns.createdAt),
  }),
)

export const ticket_types_fee = pgTable(
  'ticket_types_fee',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    applicable: boolean('applicable').default(true),
    amount: numeric('amount').notNull(),
    computed: numeric('computed').notNull(),
    type: varchar('type').notNull(),
    unit: varchar('unit').notNull(),
  },
  (columns) => ({
    _orderIdx: index('ticket_types_fee_order_idx').on(columns._order),
    _parentIDIdx: index('ticket_types_fee_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [ticket_types.id],
      name: 'ticket_types_fee_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const ticket_types = pgTable(
  'ticket_types',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    origin: enum_ticket_types_origin('origin'),
    externalDiceId_ticketTypeId: varchar('external_dice_id_ticket_type_id').notNull(),
    externalDiceId_priceTierId: varchar('external_dice_id_price_tier_id').notNull(),
    externalResidentAdvisorId: varchar('external_resident_advisor_id'),
    externalEventBriteId: varchar('external_event_brite_id'),
    ticketTypeName: varchar('ticket_type_name').notNull(),
    label: varchar('label').notNull(),
    description: varchar('description'),
    archived: boolean('archived').default(false),
    allocation: numeric('allocation').notNull(),
    faceValue: numeric('face_value').notNull(),
    totalFee: numeric('total_fee'),
    total: numeric('total'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    ticket_types_updated_at_idx: index('ticket_types_updated_at_idx').on(columns.updatedAt),
    ticket_types_created_at_idx: index('ticket_types_created_at_idx').on(columns.createdAt),
  }),
)

export const users_roles = pgTable(
  'users_roles',
  {
    order: integer('order').notNull(),
    parent: integer('parent_id').notNull(),
    value: enum_users_roles('value'),
    id: serial('id').primaryKey(),
  },
  (columns) => ({
    orderIdx: index('users_roles_order_idx').on(columns.order),
    parentIdx: index('users_roles_parent_idx').on(columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [users.id],
      name: 'users_roles_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const users_tenants_roles = pgTable(
  'users_tenants_roles',
  {
    order: integer('order').notNull(),
    parent: varchar('parent_id').notNull(),
    value: enum_users_tenants_roles('value'),
    id: serial('id').primaryKey(),
  },
  (columns) => ({
    orderIdx: index('users_tenants_roles_order_idx').on(columns.order),
    parentIdx: index('users_tenants_roles_parent_idx').on(columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [users_tenants.id],
      name: 'users_tenants_roles_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const users_tenants = pgTable(
  'users_tenants',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    tenant: integer('tenant_id')
      .notNull()
      .references(() => event_organizers.id, {
        onDelete: 'set null',
      }),
  },
  (columns) => ({
    _orderIdx: index('users_tenants_order_idx').on(columns._order),
    _parentIDIdx: index('users_tenants_parent_id_idx').on(columns._parentID),
    users_tenants_tenant_idx: index('users_tenants_tenant_idx').on(columns.tenant),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [users.id],
      name: 'users_tenants_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const users = pgTable(
  'users',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    email: varchar('email').notNull(),
    resetPasswordToken: varchar('reset_password_token'),
    resetPasswordExpiration: timestamp('reset_password_expiration', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }),
    salt: varchar('salt'),
    hash: varchar('hash'),
    loginAttempts: numeric('login_attempts').default('0'),
    lockUntil: timestamp('lock_until', { mode: 'string', withTimezone: true, precision: 3 }),
  },
  (columns) => ({
    users_updated_at_idx: index('users_updated_at_idx').on(columns.updatedAt),
    users_created_at_idx: index('users_created_at_idx').on(columns.createdAt),
    users_email_idx: uniqueIndex('users_email_idx').on(columns.email),
  }),
)

export const venues_capacities = pgTable(
  'venues_capacities',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    title: varchar('title').notNull(),
    capacity: numeric('capacity').notNull(),
  },
  (columns) => ({
    _orderIdx: index('venues_capacities_order_idx').on(columns._order),
    _parentIDIdx: index('venues_capacities_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [venues.id],
      name: 'venues_capacities_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const venues_internal_contacts = pgTable(
  'venues_internal_contacts',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    type: enum_venues_internal_contacts_type('type'),
    name: varchar('name').notNull(),
    phoneNumber: varchar('phone_number'),
    emailAddress: varchar('email_address'),
  },
  (columns) => ({
    _orderIdx: index('venues_internal_contacts_order_idx').on(columns._order),
    _parentIDIdx: index('venues_internal_contacts_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [venues.id],
      name: 'venues_internal_contacts_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const venues_social_links = pgTable(
  'venues_social_links',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    resource: enum_venues_social_links_resource('resource').notNull(),
    link: varchar('link').notNull(),
  },
  (columns) => ({
    _orderIdx: index('venues_social_links_order_idx').on(columns._order),
    _parentIDIdx: index('venues_social_links_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [venues.id],
      name: 'venues_social_links_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const venues = pgTable(
  'venues',
  {
    id: serial('id').primaryKey(),
    syncLock: boolean('sync_lock').default(false),
    name: varchar('name').notNull(),
    address: varchar('address').notNull(),
    city: varchar('city').notNull(),
    country: varchar('country').notNull(),
    coordinates: geometryColumn('coordinates'),
    timezone: varchar('timezone').notNull(),
    hub: integer('hub_id').references(() => hubs.id, {
      onDelete: 'set null',
    }),
    previewImage: integer('preview_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    houseRules: integer('house_rules_id').references(() => documents.id, {
      onDelete: 'set null',
    }),
    invoicingInfo: jsonb('invoicing_info'),
    closestAirport: varchar('closest_airport'),
    origin: enum_venues_origin('origin'),
    externalSanityId: varchar('external_sanity_id'),
    overview_overview_hero: integer('overview_overview_hero_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    overview_overview_content: jsonb('overview_overview_content'),
    meta_meta_title: varchar('meta_meta_title'),
    meta_meta_image: integer('meta_meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    meta_meta_description: varchar('meta_meta_description'),
    slug: varchar('slug').notNull(),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    venues_hub_idx: index('venues_hub_idx').on(columns.hub),
    venues_preview_image_idx: index('venues_preview_image_idx').on(columns.previewImage),
    venues_house_rules_idx: index('venues_house_rules_idx').on(columns.houseRules),
    venues_overview_overview_overview_overview_hero_idx: index(
      'venues_overview_overview_overview_overview_hero_idx',
    ).on(columns.overview_overview_hero),
    venues_meta_meta_meta_meta_image_idx: index('venues_meta_meta_meta_meta_image_idx').on(
      columns.meta_meta_image,
    ),
    venues_slug_idx: index('venues_slug_idx').on(columns.slug),
    venues_updated_at_idx: index('venues_updated_at_idx').on(columns.updatedAt),
    venues_created_at_idx: index('venues_created_at_idx').on(columns.createdAt),
  }),
)

export const venues_texts = pgTable(
  'venues_texts',
  {
    id: serial('id').primaryKey(),
    order: integer('order').notNull(),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    text: varchar('text'),
  },
  (columns) => ({
    orderParentIdx: index('venues_texts_order_parent_idx').on(columns.order, columns.parent),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [venues.id],
      name: 'venues_texts_parent_fk',
    }).onDelete('cascade'),
  }),
)

export const venues_rels = pgTable(
  'venues_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    mediaID: integer('media_id'),
    documentsID: integer('documents_id'),
  },
  (columns) => ({
    order: index('venues_rels_order_idx').on(columns.order),
    parentIdx: index('venues_rels_parent_idx').on(columns.parent),
    pathIdx: index('venues_rels_path_idx').on(columns.path),
    venues_rels_media_id_idx: index('venues_rels_media_id_idx').on(columns.mediaID),
    venues_rels_documents_id_idx: index('venues_rels_documents_id_idx').on(columns.documentsID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [venues.id],
      name: 'venues_rels_parent_fk',
    }).onDelete('cascade'),
    mediaIdFk: foreignKey({
      columns: [columns['mediaID']],
      foreignColumns: [media.id],
      name: 'venues_rels_media_fk',
    }).onDelete('cascade'),
    documentsIdFk: foreignKey({
      columns: [columns['documentsID']],
      foreignColumns: [documents.id],
      name: 'venues_rels_documents_fk',
    }).onDelete('cascade'),
  }),
)

export const redirects = pgTable(
  'redirects',
  {
    id: serial('id').primaryKey(),
    from: varchar('from').notNull(),
    to_type: enum_redirects_to_type('to_type').default('reference'),
    to_url: varchar('to_url'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    redirects_from_idx: index('redirects_from_idx').on(columns.from),
    redirects_updated_at_idx: index('redirects_updated_at_idx').on(columns.updatedAt),
    redirects_created_at_idx: index('redirects_created_at_idx').on(columns.createdAt),
  }),
)

export const redirects_rels = pgTable(
  'redirects_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    pagesID: integer('pages_id'),
  },
  (columns) => ({
    order: index('redirects_rels_order_idx').on(columns.order),
    parentIdx: index('redirects_rels_parent_idx').on(columns.parent),
    pathIdx: index('redirects_rels_path_idx').on(columns.path),
    redirects_rels_pages_id_idx: index('redirects_rels_pages_id_idx').on(columns.pagesID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [redirects.id],
      name: 'redirects_rels_parent_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'redirects_rels_pages_fk',
    }).onDelete('cascade'),
  }),
)

export const search_categories = pgTable(
  'search_categories',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    relationTo: varchar('relation_to'),
    title: varchar('title'),
  },
  (columns) => ({
    _orderIdx: index('search_categories_order_idx').on(columns._order),
    _parentIDIdx: index('search_categories_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [search.id],
      name: 'search_categories_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const search = pgTable(
  'search',
  {
    id: serial('id').primaryKey(),
    title: varchar('title'),
    priority: numeric('priority'),
    slug: varchar('slug'),
    meta_title: varchar('meta_title'),
    meta_description: varchar('meta_description'),
    meta_image: integer('meta_image_id').references(() => media.id, {
      onDelete: 'set null',
    }),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    search_slug_idx: index('search_slug_idx').on(columns.slug),
    search_meta_meta_image_idx: index('search_meta_meta_image_idx').on(columns.meta_image),
    search_updated_at_idx: index('search_updated_at_idx').on(columns.updatedAt),
    search_created_at_idx: index('search_created_at_idx').on(columns.createdAt),
  }),
)

export const search_rels = pgTable(
  'search_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    articlesID: integer('articles_id'),
  },
  (columns) => ({
    order: index('search_rels_order_idx').on(columns.order),
    parentIdx: index('search_rels_parent_idx').on(columns.parent),
    pathIdx: index('search_rels_path_idx').on(columns.path),
    search_rels_articles_id_idx: index('search_rels_articles_id_idx').on(columns.articlesID),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [search.id],
      name: 'search_rels_parent_fk',
    }).onDelete('cascade'),
    articlesIdFk: foreignKey({
      columns: [columns['articlesID']],
      foreignColumns: [articles.id],
      name: 'search_rels_articles_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_jobs_log = pgTable(
  'payload_jobs_log',
  {
    _order: integer('_order').notNull(),
    _parentID: integer('_parent_id').notNull(),
    id: varchar('id').primaryKey(),
    executedAt: timestamp('executed_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    completedAt: timestamp('completed_at', {
      mode: 'string',
      withTimezone: true,
      precision: 3,
    }).notNull(),
    taskSlug: enum_payload_jobs_log_task_slug('task_slug').notNull(),
    taskID: varchar('task_i_d').notNull(),
    input: jsonb('input'),
    output: jsonb('output'),
    state: enum_payload_jobs_log_state('state').notNull(),
    error: jsonb('error'),
  },
  (columns) => ({
    _orderIdx: index('payload_jobs_log_order_idx').on(columns._order),
    _parentIDIdx: index('payload_jobs_log_parent_id_idx').on(columns._parentID),
    _parentIDFk: foreignKey({
      columns: [columns['_parentID']],
      foreignColumns: [payload_jobs.id],
      name: 'payload_jobs_log_parent_id_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_jobs = pgTable(
  'payload_jobs',
  {
    id: serial('id').primaryKey(),
    input: jsonb('input'),
    completedAt: timestamp('completed_at', { mode: 'string', withTimezone: true, precision: 3 }),
    totalTried: numeric('total_tried').default('0'),
    hasError: boolean('has_error').default(false),
    error: jsonb('error'),
    workflowSlug: enum_payload_jobs_workflow_slug('workflow_slug'),
    taskSlug: enum_payload_jobs_task_slug('task_slug'),
    queue: varchar('queue').default('default'),
    waitUntil: timestamp('wait_until', { mode: 'string', withTimezone: true, precision: 3 }),
    processing: boolean('processing').default(false),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_jobs_completed_at_idx: index('payload_jobs_completed_at_idx').on(columns.completedAt),
    payload_jobs_total_tried_idx: index('payload_jobs_total_tried_idx').on(columns.totalTried),
    payload_jobs_has_error_idx: index('payload_jobs_has_error_idx').on(columns.hasError),
    payload_jobs_workflow_slug_idx: index('payload_jobs_workflow_slug_idx').on(
      columns.workflowSlug,
    ),
    payload_jobs_task_slug_idx: index('payload_jobs_task_slug_idx').on(columns.taskSlug),
    payload_jobs_queue_idx: index('payload_jobs_queue_idx').on(columns.queue),
    payload_jobs_wait_until_idx: index('payload_jobs_wait_until_idx').on(columns.waitUntil),
    payload_jobs_processing_idx: index('payload_jobs_processing_idx').on(columns.processing),
    payload_jobs_updated_at_idx: index('payload_jobs_updated_at_idx').on(columns.updatedAt),
    payload_jobs_created_at_idx: index('payload_jobs_created_at_idx').on(columns.createdAt),
  }),
)

export const payload_locked_documents = pgTable(
  'payload_locked_documents',
  {
    id: serial('id').primaryKey(),
    globalSlug: varchar('global_slug'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_locked_documents_global_slug_idx: index('payload_locked_documents_global_slug_idx').on(
      columns.globalSlug,
    ),
    payload_locked_documents_updated_at_idx: index('payload_locked_documents_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_locked_documents_created_at_idx: index('payload_locked_documents_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_locked_documents_rels = pgTable(
  'payload_locked_documents_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    agenciesID: integer('agencies_id'),
    agentsID: integer('agents_id'),
    articlesID: integer('articles_id'),
    artistDealsID: integer('artist_deals_id'),
    artistsID: integer('artists_id'),
    authorsID: integer('authors_id'),
    countriesID: integer('countries_id'),
    documentsID: integer('documents_id'),
    eventBrandsID: integer('event_brands_id'),
    eventOrganizersID: integer('event_organizers_id'),
    eventsID: integer('events_id'),
    fanNotificationListnersID: integer('fan_notification_listners_id'),
    fanUsersID: integer('fan_users_id'),
    festivalProfilesID: integer('festival_profiles_id'),
    festivalsID: integer('festivals_id'),
    genresID: integer('genres_id'),
    hubsID: integer('hubs_id'),
    managersID: integer('managers_id'),
    managmentCompaniesID: integer('managment_companies_id'),
    mediaID: integer('media_id'),
    ochoEpisodesID: integer('ocho_episodes_id'),
    ordersID: integer('orders_id'),
    pagesID: integer('pages_id'),
    promotionsID: integer('promotions_id'),
    residenciesID: integer('residencies_id'),
    ticketsID: integer('tickets_id'),
    ticketTypesID: integer('ticket_types_id'),
    usersID: integer('users_id'),
    venuesID: integer('venues_id'),
    redirectsID: integer('redirects_id'),
    searchID: integer('search_id'),
    'payload-jobsID': integer('payload_jobs_id'),
  },
  (columns) => ({
    order: index('payload_locked_documents_rels_order_idx').on(columns.order),
    parentIdx: index('payload_locked_documents_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_locked_documents_rels_path_idx').on(columns.path),
    payload_locked_documents_rels_agencies_id_idx: index(
      'payload_locked_documents_rels_agencies_id_idx',
    ).on(columns.agenciesID),
    payload_locked_documents_rels_agents_id_idx: index(
      'payload_locked_documents_rels_agents_id_idx',
    ).on(columns.agentsID),
    payload_locked_documents_rels_articles_id_idx: index(
      'payload_locked_documents_rels_articles_id_idx',
    ).on(columns.articlesID),
    payload_locked_documents_rels_artist_deals_id_idx: index(
      'payload_locked_documents_rels_artist_deals_id_idx',
    ).on(columns.artistDealsID),
    payload_locked_documents_rels_artists_id_idx: index(
      'payload_locked_documents_rels_artists_id_idx',
    ).on(columns.artistsID),
    payload_locked_documents_rels_authors_id_idx: index(
      'payload_locked_documents_rels_authors_id_idx',
    ).on(columns.authorsID),
    payload_locked_documents_rels_countries_id_idx: index(
      'payload_locked_documents_rels_countries_id_idx',
    ).on(columns.countriesID),
    payload_locked_documents_rels_documents_id_idx: index(
      'payload_locked_documents_rels_documents_id_idx',
    ).on(columns.documentsID),
    payload_locked_documents_rels_event_brands_id_idx: index(
      'payload_locked_documents_rels_event_brands_id_idx',
    ).on(columns.eventBrandsID),
    payload_locked_documents_rels_event_organizers_id_idx: index(
      'payload_locked_documents_rels_event_organizers_id_idx',
    ).on(columns.eventOrganizersID),
    payload_locked_documents_rels_events_id_idx: index(
      'payload_locked_documents_rels_events_id_idx',
    ).on(columns.eventsID),
    payload_locked_documents_rels_fan_notification_listners_id_idx: index(
      'payload_locked_documents_rels_fan_notification_listners_id_idx',
    ).on(columns.fanNotificationListnersID),
    payload_locked_documents_rels_fan_users_id_idx: index(
      'payload_locked_documents_rels_fan_users_id_idx',
    ).on(columns.fanUsersID),
    payload_locked_documents_rels_festival_profiles_id_idx: index(
      'payload_locked_documents_rels_festival_profiles_id_idx',
    ).on(columns.festivalProfilesID),
    payload_locked_documents_rels_festivals_id_idx: index(
      'payload_locked_documents_rels_festivals_id_idx',
    ).on(columns.festivalsID),
    payload_locked_documents_rels_genres_id_idx: index(
      'payload_locked_documents_rels_genres_id_idx',
    ).on(columns.genresID),
    payload_locked_documents_rels_hubs_id_idx: index(
      'payload_locked_documents_rels_hubs_id_idx',
    ).on(columns.hubsID),
    payload_locked_documents_rels_managers_id_idx: index(
      'payload_locked_documents_rels_managers_id_idx',
    ).on(columns.managersID),
    payload_locked_documents_rels_managment_companies_id_idx: index(
      'payload_locked_documents_rels_managment_companies_id_idx',
    ).on(columns.managmentCompaniesID),
    payload_locked_documents_rels_media_id_idx: index(
      'payload_locked_documents_rels_media_id_idx',
    ).on(columns.mediaID),
    payload_locked_documents_rels_ocho_episodes_id_idx: index(
      'payload_locked_documents_rels_ocho_episodes_id_idx',
    ).on(columns.ochoEpisodesID),
    payload_locked_documents_rels_orders_id_idx: index(
      'payload_locked_documents_rels_orders_id_idx',
    ).on(columns.ordersID),
    payload_locked_documents_rels_pages_id_idx: index(
      'payload_locked_documents_rels_pages_id_idx',
    ).on(columns.pagesID),
    payload_locked_documents_rels_promotions_id_idx: index(
      'payload_locked_documents_rels_promotions_id_idx',
    ).on(columns.promotionsID),
    payload_locked_documents_rels_residencies_id_idx: index(
      'payload_locked_documents_rels_residencies_id_idx',
    ).on(columns.residenciesID),
    payload_locked_documents_rels_tickets_id_idx: index(
      'payload_locked_documents_rels_tickets_id_idx',
    ).on(columns.ticketsID),
    payload_locked_documents_rels_ticket_types_id_idx: index(
      'payload_locked_documents_rels_ticket_types_id_idx',
    ).on(columns.ticketTypesID),
    payload_locked_documents_rels_users_id_idx: index(
      'payload_locked_documents_rels_users_id_idx',
    ).on(columns.usersID),
    payload_locked_documents_rels_venues_id_idx: index(
      'payload_locked_documents_rels_venues_id_idx',
    ).on(columns.venuesID),
    payload_locked_documents_rels_redirects_id_idx: index(
      'payload_locked_documents_rels_redirects_id_idx',
    ).on(columns.redirectsID),
    payload_locked_documents_rels_search_id_idx: index(
      'payload_locked_documents_rels_search_id_idx',
    ).on(columns.searchID),
    payload_locked_documents_rels_payload_jobs_id_idx: index(
      'payload_locked_documents_rels_payload_jobs_id_idx',
    ).on(columns['payload-jobsID']),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_locked_documents.id],
      name: 'payload_locked_documents_rels_parent_fk',
    }).onDelete('cascade'),
    agenciesIdFk: foreignKey({
      columns: [columns['agenciesID']],
      foreignColumns: [agencies.id],
      name: 'payload_locked_documents_rels_agencies_fk',
    }).onDelete('cascade'),
    agentsIdFk: foreignKey({
      columns: [columns['agentsID']],
      foreignColumns: [agents.id],
      name: 'payload_locked_documents_rels_agents_fk',
    }).onDelete('cascade'),
    articlesIdFk: foreignKey({
      columns: [columns['articlesID']],
      foreignColumns: [articles.id],
      name: 'payload_locked_documents_rels_articles_fk',
    }).onDelete('cascade'),
    artistDealsIdFk: foreignKey({
      columns: [columns['artistDealsID']],
      foreignColumns: [artist_deals.id],
      name: 'payload_locked_documents_rels_artist_deals_fk',
    }).onDelete('cascade'),
    artistsIdFk: foreignKey({
      columns: [columns['artistsID']],
      foreignColumns: [artists.id],
      name: 'payload_locked_documents_rels_artists_fk',
    }).onDelete('cascade'),
    authorsIdFk: foreignKey({
      columns: [columns['authorsID']],
      foreignColumns: [authors.id],
      name: 'payload_locked_documents_rels_authors_fk',
    }).onDelete('cascade'),
    countriesIdFk: foreignKey({
      columns: [columns['countriesID']],
      foreignColumns: [countries.id],
      name: 'payload_locked_documents_rels_countries_fk',
    }).onDelete('cascade'),
    documentsIdFk: foreignKey({
      columns: [columns['documentsID']],
      foreignColumns: [documents.id],
      name: 'payload_locked_documents_rels_documents_fk',
    }).onDelete('cascade'),
    eventBrandsIdFk: foreignKey({
      columns: [columns['eventBrandsID']],
      foreignColumns: [event_brands.id],
      name: 'payload_locked_documents_rels_event_brands_fk',
    }).onDelete('cascade'),
    eventOrganizersIdFk: foreignKey({
      columns: [columns['eventOrganizersID']],
      foreignColumns: [event_organizers.id],
      name: 'payload_locked_documents_rels_event_organizers_fk',
    }).onDelete('cascade'),
    eventsIdFk: foreignKey({
      columns: [columns['eventsID']],
      foreignColumns: [events.id],
      name: 'payload_locked_documents_rels_events_fk',
    }).onDelete('cascade'),
    fanNotificationListnersIdFk: foreignKey({
      columns: [columns['fanNotificationListnersID']],
      foreignColumns: [fan_notification_listners.id],
      name: 'payload_locked_documents_rels_fan_notification_listners_fk',
    }).onDelete('cascade'),
    fanUsersIdFk: foreignKey({
      columns: [columns['fanUsersID']],
      foreignColumns: [fan_users.id],
      name: 'payload_locked_documents_rels_fan_users_fk',
    }).onDelete('cascade'),
    festivalProfilesIdFk: foreignKey({
      columns: [columns['festivalProfilesID']],
      foreignColumns: [festival_profiles.id],
      name: 'payload_locked_documents_rels_festival_profiles_fk',
    }).onDelete('cascade'),
    festivalsIdFk: foreignKey({
      columns: [columns['festivalsID']],
      foreignColumns: [festivals.id],
      name: 'payload_locked_documents_rels_festivals_fk',
    }).onDelete('cascade'),
    genresIdFk: foreignKey({
      columns: [columns['genresID']],
      foreignColumns: [genres.id],
      name: 'payload_locked_documents_rels_genres_fk',
    }).onDelete('cascade'),
    hubsIdFk: foreignKey({
      columns: [columns['hubsID']],
      foreignColumns: [hubs.id],
      name: 'payload_locked_documents_rels_hubs_fk',
    }).onDelete('cascade'),
    managersIdFk: foreignKey({
      columns: [columns['managersID']],
      foreignColumns: [managers.id],
      name: 'payload_locked_documents_rels_managers_fk',
    }).onDelete('cascade'),
    managmentCompaniesIdFk: foreignKey({
      columns: [columns['managmentCompaniesID']],
      foreignColumns: [managment_companies.id],
      name: 'payload_locked_documents_rels_managment_companies_fk',
    }).onDelete('cascade'),
    mediaIdFk: foreignKey({
      columns: [columns['mediaID']],
      foreignColumns: [media.id],
      name: 'payload_locked_documents_rels_media_fk',
    }).onDelete('cascade'),
    ochoEpisodesIdFk: foreignKey({
      columns: [columns['ochoEpisodesID']],
      foreignColumns: [ocho_episodes.id],
      name: 'payload_locked_documents_rels_ocho_episodes_fk',
    }).onDelete('cascade'),
    ordersIdFk: foreignKey({
      columns: [columns['ordersID']],
      foreignColumns: [orders.id],
      name: 'payload_locked_documents_rels_orders_fk',
    }).onDelete('cascade'),
    pagesIdFk: foreignKey({
      columns: [columns['pagesID']],
      foreignColumns: [pages.id],
      name: 'payload_locked_documents_rels_pages_fk',
    }).onDelete('cascade'),
    promotionsIdFk: foreignKey({
      columns: [columns['promotionsID']],
      foreignColumns: [promotions.id],
      name: 'payload_locked_documents_rels_promotions_fk',
    }).onDelete('cascade'),
    residenciesIdFk: foreignKey({
      columns: [columns['residenciesID']],
      foreignColumns: [residencies.id],
      name: 'payload_locked_documents_rels_residencies_fk',
    }).onDelete('cascade'),
    ticketsIdFk: foreignKey({
      columns: [columns['ticketsID']],
      foreignColumns: [tickets.id],
      name: 'payload_locked_documents_rels_tickets_fk',
    }).onDelete('cascade'),
    ticketTypesIdFk: foreignKey({
      columns: [columns['ticketTypesID']],
      foreignColumns: [ticket_types.id],
      name: 'payload_locked_documents_rels_ticket_types_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_locked_documents_rels_users_fk',
    }).onDelete('cascade'),
    venuesIdFk: foreignKey({
      columns: [columns['venuesID']],
      foreignColumns: [venues.id],
      name: 'payload_locked_documents_rels_venues_fk',
    }).onDelete('cascade'),
    redirectsIdFk: foreignKey({
      columns: [columns['redirectsID']],
      foreignColumns: [redirects.id],
      name: 'payload_locked_documents_rels_redirects_fk',
    }).onDelete('cascade'),
    searchIdFk: foreignKey({
      columns: [columns['searchID']],
      foreignColumns: [search.id],
      name: 'payload_locked_documents_rels_search_fk',
    }).onDelete('cascade'),
    'payload-jobsIdFk': foreignKey({
      columns: [columns['payload-jobsID']],
      foreignColumns: [payload_jobs.id],
      name: 'payload_locked_documents_rels_payload_jobs_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_preferences = pgTable(
  'payload_preferences',
  {
    id: serial('id').primaryKey(),
    key: varchar('key'),
    value: jsonb('value'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_preferences_key_idx: index('payload_preferences_key_idx').on(columns.key),
    payload_preferences_updated_at_idx: index('payload_preferences_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_preferences_created_at_idx: index('payload_preferences_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const payload_preferences_rels = pgTable(
  'payload_preferences_rels',
  {
    id: serial('id').primaryKey(),
    order: integer('order'),
    parent: integer('parent_id').notNull(),
    path: varchar('path').notNull(),
    usersID: integer('users_id'),
  },
  (columns) => ({
    order: index('payload_preferences_rels_order_idx').on(columns.order),
    parentIdx: index('payload_preferences_rels_parent_idx').on(columns.parent),
    pathIdx: index('payload_preferences_rels_path_idx').on(columns.path),
    payload_preferences_rels_users_id_idx: index('payload_preferences_rels_users_id_idx').on(
      columns.usersID,
    ),
    parentFk: foreignKey({
      columns: [columns['parent']],
      foreignColumns: [payload_preferences.id],
      name: 'payload_preferences_rels_parent_fk',
    }).onDelete('cascade'),
    usersIdFk: foreignKey({
      columns: [columns['usersID']],
      foreignColumns: [users.id],
      name: 'payload_preferences_rels_users_fk',
    }).onDelete('cascade'),
  }),
)

export const payload_migrations = pgTable(
  'payload_migrations',
  {
    id: serial('id').primaryKey(),
    name: varchar('name'),
    batch: numeric('batch'),
    updatedAt: timestamp('updated_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
    createdAt: timestamp('created_at', { mode: 'string', withTimezone: true, precision: 3 })
      .defaultNow()
      .notNull(),
  },
  (columns) => ({
    payload_migrations_updated_at_idx: index('payload_migrations_updated_at_idx').on(
      columns.updatedAt,
    ),
    payload_migrations_created_at_idx: index('payload_migrations_created_at_idx').on(
      columns.createdAt,
    ),
  }),
)

export const relations_agencies = relations(agencies, () => ({}))
export const relations_agents_texts = relations(agents_texts, ({ one }) => ({
  parent: one(agents, {
    fields: [agents_texts.parent],
    references: [agents.id],
    relationName: '_texts',
  }),
}))
export const relations_agents = relations(agents, ({ one, many }) => ({
  agency: one(agencies, {
    fields: [agents.agency],
    references: [agencies.id],
    relationName: 'agency',
  }),
  _texts: many(agents_texts, {
    relationName: '_texts',
  }),
}))
export const relations_articles_rels = relations(articles_rels, ({ one }) => ({
  parent: one(articles, {
    fields: [articles_rels.parent],
    references: [articles.id],
    relationName: '_rels',
  }),
  eventsID: one(events, {
    fields: [articles_rels.eventsID],
    references: [events.id],
    relationName: 'events',
  }),
  artistsID: one(artists, {
    fields: [articles_rels.artistsID],
    references: [artists.id],
    relationName: 'artists',
  }),
  eventBrandsID: one(event_brands, {
    fields: [articles_rels.eventBrandsID],
    references: [event_brands.id],
    relationName: 'eventBrands',
  }),
  festivalsID: one(festivals, {
    fields: [articles_rels.festivalsID],
    references: [festivals.id],
    relationName: 'festivals',
  }),
  residenciesID: one(residencies, {
    fields: [articles_rels.residenciesID],
    references: [residencies.id],
    relationName: 'residencies',
  }),
  venuesID: one(venues, {
    fields: [articles_rels.venuesID],
    references: [venues.id],
    relationName: 'venues',
  }),
  authorsID: one(authors, {
    fields: [articles_rels.authorsID],
    references: [authors.id],
    relationName: 'authors',
  }),
  ochoEpisodesID: one(ocho_episodes, {
    fields: [articles_rels.ochoEpisodesID],
    references: [ocho_episodes.id],
    relationName: 'ochoEpisodes',
  }),
  hubsID: one(hubs, {
    fields: [articles_rels.hubsID],
    references: [hubs.id],
    relationName: 'hubs',
  }),
}))
export const relations_articles = relations(articles, ({ one, many }) => ({
  author: one(authors, {
    fields: [articles.author],
    references: [authors.id],
    relationName: 'author',
  }),
  previewImage: one(media, {
    fields: [articles.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  overview_overview_hero: one(media, {
    fields: [articles.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [articles.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
  _rels: many(articles_rels, {
    relationName: '_rels',
  }),
}))
export const relations_artist_deals_status_history = relations(
  artist_deals_status_history,
  ({ one }) => ({
    _parentID: one(artist_deals, {
      fields: [artist_deals_status_history._parentID],
      references: [artist_deals.id],
      relationName: 'statusHistory',
    }),
  }),
)
export const relations_artist_deals_rels = relations(artist_deals_rels, ({ one }) => ({
  parent: one(artist_deals, {
    fields: [artist_deals_rels.parent],
    references: [artist_deals.id],
    relationName: '_rels',
  }),
  documentsID: one(documents, {
    fields: [artist_deals_rels.documentsID],
    references: [documents.id],
    relationName: 'documents',
  }),
}))
export const relations_artist_deals = relations(artist_deals, ({ one, many }) => ({
  artist: one(artists, {
    fields: [artist_deals.artist],
    references: [artists.id],
    relationName: 'artist',
  }),
  event: one(events, {
    fields: [artist_deals.event],
    references: [events.id],
    relationName: 'event',
  }),
  statusHistory: many(artist_deals_status_history, {
    relationName: 'statusHistory',
  }),
  _rels: many(artist_deals_rels, {
    relationName: '_rels',
  }),
}))
export const relations_artists_agency_territory_representations = relations(
  artists_agency_territory_representations,
  ({ one }) => ({
    _parentID: one(artists, {
      fields: [artists_agency_territory_representations._parentID],
      references: [artists.id],
      relationName: 'agencyTerritoryRepresentations',
    }),
    agency: one(agencies, {
      fields: [artists_agency_territory_representations.agency],
      references: [agencies.id],
      relationName: 'agency',
    }),
  }),
)
export const relations_artists_management_representations = relations(
  artists_management_representations,
  ({ one }) => ({
    _parentID: one(artists, {
      fields: [artists_management_representations._parentID],
      references: [artists.id],
      relationName: 'managementRepresentations',
    }),
  }),
)
export const relations_artists_social_links = relations(artists_social_links, ({ one }) => ({
  _parentID: one(artists, {
    fields: [artists_social_links._parentID],
    references: [artists.id],
    relationName: 'socialLinks',
  }),
}))
export const relations_artists_texts = relations(artists_texts, ({ one }) => ({
  parent: one(artists, {
    fields: [artists_texts.parent],
    references: [artists.id],
    relationName: '_texts',
  }),
}))
export const relations_artists_rels = relations(artists_rels, ({ one }) => ({
  parent: one(artists, {
    fields: [artists_rels.parent],
    references: [artists.id],
    relationName: '_rels',
  }),
  genresID: one(genres, {
    fields: [artists_rels.genresID],
    references: [genres.id],
    relationName: 'genres',
  }),
  residenciesID: one(residencies, {
    fields: [artists_rels.residenciesID],
    references: [residencies.id],
    relationName: 'residencies',
  }),
  countriesID: one(countries, {
    fields: [artists_rels.countriesID],
    references: [countries.id],
    relationName: 'countries',
  }),
  agentsID: one(agents, {
    fields: [artists_rels.agentsID],
    references: [agents.id],
    relationName: 'agents',
  }),
  managersID: one(managers, {
    fields: [artists_rels.managersID],
    references: [managers.id],
    relationName: 'managers',
  }),
}))
export const relations_artists = relations(artists, ({ one, many }) => ({
  country: one(countries, {
    fields: [artists.country],
    references: [countries.id],
    relationName: 'country',
  }),
  previewImage: one(media, {
    fields: [artists.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  agencyTerritoryRepresentations: many(artists_agency_territory_representations, {
    relationName: 'agencyTerritoryRepresentations',
  }),
  managementRepresentations: many(artists_management_representations, {
    relationName: 'managementRepresentations',
  }),
  socialLinks: many(artists_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [artists.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [artists.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
  _texts: many(artists_texts, {
    relationName: '_texts',
  }),
  _rels: many(artists_rels, {
    relationName: '_rels',
  }),
}))
export const relations_authors_social_links = relations(authors_social_links, ({ one }) => ({
  _parentID: one(authors, {
    fields: [authors_social_links._parentID],
    references: [authors.id],
    relationName: 'socialLinks',
  }),
}))
export const relations_authors = relations(authors, ({ one, many }) => ({
  country: one(countries, {
    fields: [authors.country],
    references: [countries.id],
    relationName: 'country',
  }),
  previewImage: one(media, {
    fields: [authors.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(authors_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [authors.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [authors.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_countries = relations(countries, ({ one }) => ({
  flag: one(media, {
    fields: [countries.flag],
    references: [media.id],
    relationName: 'flag',
  }),
}))
export const relations_documents = relations(documents, () => ({}))
export const relations_event_brands_social_links = relations(
  event_brands_social_links,
  ({ one }) => ({
    _parentID: one(event_brands, {
      fields: [event_brands_social_links._parentID],
      references: [event_brands.id],
      relationName: 'socialLinks',
    }),
  }),
)
export const relations_event_brands = relations(event_brands, ({ one, many }) => ({
  country: one(countries, {
    fields: [event_brands.country],
    references: [countries.id],
    relationName: 'country',
  }),
  previewImage: one(media, {
    fields: [event_brands.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(event_brands_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [event_brands.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [event_brands.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_event_organizers = relations(event_organizers, ({ one }) => ({
  overview_overview_hero: one(media, {
    fields: [event_organizers.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [event_organizers.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_events_external_platform_source_urls = relations(
  events_external_platform_source_urls,
  ({ one }) => ({
    _parentID: one(events, {
      fields: [events_external_platform_source_urls._parentID],
      references: [events.id],
      relationName: 'externalPlatformSourceUrls',
    }),
  }),
)
export const relations_events_lineup = relations(events_lineup, ({ one }) => ({
  _parentID: one(events, {
    fields: [events_lineup._parentID],
    references: [events.id],
    relationName: 'lineup',
  }),
  artist: one(artists, {
    fields: [events_lineup.artist],
    references: [artists.id],
    relationName: 'artist',
  }),
}))
export const relations_events_social_links = relations(events_social_links, ({ one }) => ({
  _parentID: one(events, {
    fields: [events_social_links._parentID],
    references: [events.id],
    relationName: 'socialLinks',
  }),
}))
export const relations_events_faqs = relations(events_faqs, ({ one }) => ({
  _parentID: one(events, {
    fields: [events_faqs._parentID],
    references: [events.id],
    relationName: 'faqs',
  }),
}))
export const relations_events_ticket_types = relations(events_ticket_types, ({ one }) => ({
  _parentID: one(events, {
    fields: [events_ticket_types._parentID],
    references: [events.id],
    relationName: 'ticketTypes',
  }),
  ticketType: one(ticket_types, {
    fields: [events_ticket_types.ticketType],
    references: [ticket_types.id],
    relationName: 'ticketType',
  }),
}))
export const relations_events_marketing_tracking_links = relations(
  events_marketing_tracking_links,
  ({ one }) => ({
    _parentID: one(events, {
      fields: [events_marketing_tracking_links._parentID],
      references: [events.id],
      relationName: 'marketing_trackingLinks',
    }),
  }),
)
export const relations_events_texts = relations(events_texts, ({ one }) => ({
  parent: one(events, {
    fields: [events_texts.parent],
    references: [events.id],
    relationName: '_texts',
  }),
}))
export const relations_events_rels = relations(events_rels, ({ one }) => ({
  parent: one(events, {
    fields: [events_rels.parent],
    references: [events.id],
    relationName: '_rels',
  }),
  genresID: one(genres, {
    fields: [events_rels.genresID],
    references: [genres.id],
    relationName: 'genres',
  }),
  eventsID: one(events, {
    fields: [events_rels.eventsID],
    references: [events.id],
    relationName: 'events',
  }),
  promotionsID: one(promotions, {
    fields: [events_rels.promotionsID],
    references: [promotions.id],
    relationName: 'promotions',
  }),
}))
export const relations_events = relations(events, ({ one, many }) => ({
  eventOrganizer: one(event_organizers, {
    fields: [events.eventOrganizer],
    references: [event_organizers.id],
    relationName: 'eventOrganizer',
  }),
  externalPlatformSourceUrls: many(events_external_platform_source_urls, {
    relationName: 'externalPlatformSourceUrls',
  }),
  festival: one(festivals, {
    fields: [events.festival],
    references: [festivals.id],
    relationName: 'festival',
  }),
  eventBrand: one(event_brands, {
    fields: [events.eventBrand],
    references: [event_brands.id],
    relationName: 'eventBrand',
  }),
  residency: one(residencies, {
    fields: [events.residency],
    references: [residencies.id],
    relationName: 'residency',
  }),
  Location_venue: one(venues, {
    fields: [events.Location_venue],
    references: [venues.id],
    relationName: 'Location_venue',
  }),
  Location_hub: one(hubs, {
    fields: [events.Location_hub],
    references: [hubs.id],
    relationName: 'Location_hub',
  }),
  lineup: many(events_lineup, {
    relationName: 'lineup',
  }),
  previewImage: one(media, {
    fields: [events.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(events_social_links, {
    relationName: 'socialLinks',
  }),
  faqs: many(events_faqs, {
    relationName: 'faqs',
  }),
  ticketTypes: many(events_ticket_types, {
    relationName: 'ticketTypes',
  }),
  overview_overview_hero: one(media, {
    fields: [events.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  marketing_trackingLinks: many(events_marketing_tracking_links, {
    relationName: 'marketing_trackingLinks',
  }),
  meta_meta_image: one(media, {
    fields: [events.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
  _texts: many(events_texts, {
    relationName: '_texts',
  }),
  _rels: many(events_rels, {
    relationName: '_rels',
  }),
}))
export const relations_fan_notification_listners = relations(
  fan_notification_listners,
  ({ one }) => ({
    event: one(events, {
      fields: [fan_notification_listners.event],
      references: [events.id],
      relationName: 'event',
    }),
    fan: one(fan_users, {
      fields: [fan_notification_listners.fan],
      references: [fan_users.id],
      relationName: 'fan',
    }),
  }),
)
export const relations_fan_users_rels = relations(fan_users_rels, ({ one }) => ({
  parent: one(fan_users, {
    fields: [fan_users_rels.parent],
    references: [fan_users.id],
    relationName: '_rels',
  }),
  eventBrandsID: one(event_brands, {
    fields: [fan_users_rels.eventBrandsID],
    references: [event_brands.id],
    relationName: 'eventBrands',
  }),
  genresID: one(genres, {
    fields: [fan_users_rels.genresID],
    references: [genres.id],
    relationName: 'genres',
  }),
  artistsID: one(artists, {
    fields: [fan_users_rels.artistsID],
    references: [artists.id],
    relationName: 'artists',
  }),
  authorsID: one(authors, {
    fields: [fan_users_rels.authorsID],
    references: [authors.id],
    relationName: 'authors',
  }),
  ordersID: one(orders, {
    fields: [fan_users_rels.ordersID],
    references: [orders.id],
    relationName: 'orders',
  }),
}))
export const relations_fan_users = relations(fan_users, ({ many }) => ({
  _rels: many(fan_users_rels, {
    relationName: '_rels',
  }),
}))
export const relations_festival_profiles_social_links = relations(
  festival_profiles_social_links,
  ({ one }) => ({
    _parentID: one(festival_profiles, {
      fields: [festival_profiles_social_links._parentID],
      references: [festival_profiles.id],
      relationName: 'socialLinks',
    }),
  }),
)
export const relations_festival_profiles = relations(festival_profiles, ({ one, many }) => ({
  previewImage: one(media, {
    fields: [festival_profiles.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(festival_profiles_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [festival_profiles.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [festival_profiles.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_festivals_social_links = relations(festivals_social_links, ({ one }) => ({
  _parentID: one(festivals, {
    fields: [festivals_social_links._parentID],
    references: [festivals.id],
    relationName: 'socialLinks',
  }),
}))
export const relations_festivals = relations(festivals, ({ one, many }) => ({
  festivalBrand: one(festival_profiles, {
    fields: [festivals.festivalBrand],
    references: [festival_profiles.id],
    relationName: 'festivalBrand',
  }),
  previewImage: one(media, {
    fields: [festivals.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(festivals_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [festivals.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [festivals.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_genres = relations(genres, ({ one }) => ({
  overview_overview_hero: one(media, {
    fields: [genres.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [genres.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_hubs = relations(hubs, ({ one }) => ({
  parent: one(hubs, {
    fields: [hubs.parent],
    references: [hubs.id],
    relationName: 'parent',
  }),
  overview_overview_hero: one(media, {
    fields: [hubs.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [hubs.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_managers = relations(managers, ({ one }) => ({
  managmentCompany: one(managment_companies, {
    fields: [managers.managmentCompany],
    references: [managment_companies.id],
    relationName: 'managmentCompany',
  }),
}))
export const relations_managment_companies = relations(managment_companies, () => ({}))
export const relations_media = relations(media, () => ({}))
export const relations_ocho_episodes_rels = relations(ocho_episodes_rels, ({ one }) => ({
  parent: one(ocho_episodes, {
    fields: [ocho_episodes_rels.parent],
    references: [ocho_episodes.id],
    relationName: '_rels',
  }),
  artistsID: one(artists, {
    fields: [ocho_episodes_rels.artistsID],
    references: [artists.id],
    relationName: 'artists',
  }),
}))
export const relations_ocho_episodes = relations(ocho_episodes, ({ one, many }) => ({
  previewImage: one(media, {
    fields: [ocho_episodes.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  overview_overview_hero: one(media, {
    fields: [ocho_episodes.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [ocho_episodes.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
  _rels: many(ocho_episodes_rels, {
    relationName: '_rels',
  }),
}))
export const relations_orders_rels = relations(orders_rels, ({ one }) => ({
  parent: one(orders, {
    fields: [orders_rels.parent],
    references: [orders.id],
    relationName: '_rels',
  }),
  ticketsID: one(tickets, {
    fields: [orders_rels.ticketsID],
    references: [tickets.id],
    relationName: 'tickets',
  }),
}))
export const relations_orders = relations(orders, ({ one, many }) => ({
  event: one(events, {
    fields: [orders.event],
    references: [events.id],
    relationName: 'event',
  }),
  fan: one(fan_users, {
    fields: [orders.fan],
    references: [fan_users.id],
    relationName: 'fan',
  }),
  _rels: many(orders_rels, {
    relationName: '_rels',
  }),
}))
export const relations_pages_blocks_cta_links = relations(pages_blocks_cta_links, ({ one }) => ({
  _parentID: one(pages_blocks_cta, {
    fields: [pages_blocks_cta_links._parentID],
    references: [pages_blocks_cta.id],
    relationName: 'links',
  }),
}))
export const relations_pages_blocks_cta = relations(pages_blocks_cta, ({ one, many }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_cta._parentID],
    references: [pages.id],
    relationName: '_blocks_cta',
  }),
  links: many(pages_blocks_cta_links, {
    relationName: 'links',
  }),
}))
export const relations_pages_blocks_content_columns = relations(
  pages_blocks_content_columns,
  ({ one }) => ({
    _parentID: one(pages_blocks_content, {
      fields: [pages_blocks_content_columns._parentID],
      references: [pages_blocks_content.id],
      relationName: 'columns',
    }),
  }),
)
export const relations_pages_blocks_content = relations(pages_blocks_content, ({ one, many }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_content._parentID],
    references: [pages.id],
    relationName: '_blocks_content',
  }),
  columns: many(pages_blocks_content_columns, {
    relationName: 'columns',
  }),
}))
export const relations_pages_blocks_media_block = relations(
  pages_blocks_media_block,
  ({ one }) => ({
    _parentID: one(pages, {
      fields: [pages_blocks_media_block._parentID],
      references: [pages.id],
      relationName: '_blocks_mediaBlock',
    }),
    media: one(media, {
      fields: [pages_blocks_media_block.media],
      references: [media.id],
      relationName: 'media',
    }),
  }),
)
export const relations_pages_blocks_archive = relations(pages_blocks_archive, ({ one }) => ({
  _parentID: one(pages, {
    fields: [pages_blocks_archive._parentID],
    references: [pages.id],
    relationName: '_blocks_archive',
  }),
}))
export const relations_pages_blocks_events_block = relations(
  pages_blocks_events_block,
  ({ one }) => ({
    _parentID: one(pages, {
      fields: [pages_blocks_events_block._parentID],
      references: [pages.id],
      relationName: '_blocks_eventsBlock',
    }),
  }),
)
export const relations_pages_blocks_home_events_section = relations(
  pages_blocks_home_events_section,
  ({ one }) => ({
    _parentID: one(pages, {
      fields: [pages_blocks_home_events_section._parentID],
      references: [pages.id],
      relationName: '_blocks_homeEventsSection',
    }),
  }),
)
export const relations_pages_rels = relations(pages_rels, ({ one }) => ({
  parent: one(pages, {
    fields: [pages_rels.parent],
    references: [pages.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [pages_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  eventsID: one(events, {
    fields: [pages_rels.eventsID],
    references: [events.id],
    relationName: 'events',
  }),
}))
export const relations_pages = relations(pages, ({ one, many }) => ({
  _blocks_cta: many(pages_blocks_cta, {
    relationName: '_blocks_cta',
  }),
  _blocks_content: many(pages_blocks_content, {
    relationName: '_blocks_content',
  }),
  _blocks_mediaBlock: many(pages_blocks_media_block, {
    relationName: '_blocks_mediaBlock',
  }),
  _blocks_archive: many(pages_blocks_archive, {
    relationName: '_blocks_archive',
  }),
  _blocks_eventsBlock: many(pages_blocks_events_block, {
    relationName: '_blocks_eventsBlock',
  }),
  _blocks_homeEventsSection: many(pages_blocks_home_events_section, {
    relationName: '_blocks_homeEventsSection',
  }),
  meta_image: one(media, {
    fields: [pages.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  _rels: many(pages_rels, {
    relationName: '_rels',
  }),
}))
export const relations__pages_v_blocks_cta_links = relations(
  _pages_v_blocks_cta_links,
  ({ one }) => ({
    _parentID: one(_pages_v_blocks_cta, {
      fields: [_pages_v_blocks_cta_links._parentID],
      references: [_pages_v_blocks_cta.id],
      relationName: 'links',
    }),
  }),
)
export const relations__pages_v_blocks_cta = relations(_pages_v_blocks_cta, ({ one, many }) => ({
  _parentID: one(_pages_v, {
    fields: [_pages_v_blocks_cta._parentID],
    references: [_pages_v.id],
    relationName: '_blocks_cta',
  }),
  links: many(_pages_v_blocks_cta_links, {
    relationName: 'links',
  }),
}))
export const relations__pages_v_blocks_content_columns = relations(
  _pages_v_blocks_content_columns,
  ({ one }) => ({
    _parentID: one(_pages_v_blocks_content, {
      fields: [_pages_v_blocks_content_columns._parentID],
      references: [_pages_v_blocks_content.id],
      relationName: 'columns',
    }),
  }),
)
export const relations__pages_v_blocks_content = relations(
  _pages_v_blocks_content,
  ({ one, many }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_content._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_content',
    }),
    columns: many(_pages_v_blocks_content_columns, {
      relationName: 'columns',
    }),
  }),
)
export const relations__pages_v_blocks_media_block = relations(
  _pages_v_blocks_media_block,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_media_block._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_mediaBlock',
    }),
    media: one(media, {
      fields: [_pages_v_blocks_media_block.media],
      references: [media.id],
      relationName: 'media',
    }),
  }),
)
export const relations__pages_v_blocks_archive = relations(_pages_v_blocks_archive, ({ one }) => ({
  _parentID: one(_pages_v, {
    fields: [_pages_v_blocks_archive._parentID],
    references: [_pages_v.id],
    relationName: '_blocks_archive',
  }),
}))
export const relations__pages_v_blocks_events_block = relations(
  _pages_v_blocks_events_block,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_events_block._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_eventsBlock',
    }),
  }),
)
export const relations__pages_v_blocks_home_events_section = relations(
  _pages_v_blocks_home_events_section,
  ({ one }) => ({
    _parentID: one(_pages_v, {
      fields: [_pages_v_blocks_home_events_section._parentID],
      references: [_pages_v.id],
      relationName: '_blocks_homeEventsSection',
    }),
  }),
)
export const relations__pages_v_rels = relations(_pages_v_rels, ({ one }) => ({
  parent: one(_pages_v, {
    fields: [_pages_v_rels.parent],
    references: [_pages_v.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [_pages_v_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
  eventsID: one(events, {
    fields: [_pages_v_rels.eventsID],
    references: [events.id],
    relationName: 'events',
  }),
}))
export const relations__pages_v = relations(_pages_v, ({ one, many }) => ({
  parent: one(pages, {
    fields: [_pages_v.parent],
    references: [pages.id],
    relationName: 'parent',
  }),
  _blocks_cta: many(_pages_v_blocks_cta, {
    relationName: '_blocks_cta',
  }),
  _blocks_content: many(_pages_v_blocks_content, {
    relationName: '_blocks_content',
  }),
  _blocks_mediaBlock: many(_pages_v_blocks_media_block, {
    relationName: '_blocks_mediaBlock',
  }),
  _blocks_archive: many(_pages_v_blocks_archive, {
    relationName: '_blocks_archive',
  }),
  _blocks_eventsBlock: many(_pages_v_blocks_events_block, {
    relationName: '_blocks_eventsBlock',
  }),
  _blocks_homeEventsSection: many(_pages_v_blocks_home_events_section, {
    relationName: '_blocks_homeEventsSection',
  }),
  version_meta_image: one(media, {
    fields: [_pages_v.version_meta_image],
    references: [media.id],
    relationName: 'version_meta_image',
  }),
  _rels: many(_pages_v_rels, {
    relationName: '_rels',
  }),
}))
export const relations_promotions_code_locks = relations(promotions_code_locks, ({ one }) => ({
  _parentID: one(promotions, {
    fields: [promotions_code_locks._parentID],
    references: [promotions.id],
    relationName: 'codeLocks',
  }),
  claimedBy: one(fan_users, {
    fields: [promotions_code_locks.claimedBy],
    references: [fan_users.id],
    relationName: 'claimedBy',
  }),
}))
export const relations_promotions = relations(promotions, ({ many }) => ({
  codeLocks: many(promotions_code_locks, {
    relationName: 'codeLocks',
  }),
}))
export const relations_residencies_social_links = relations(
  residencies_social_links,
  ({ one }) => ({
    _parentID: one(residencies, {
      fields: [residencies_social_links._parentID],
      references: [residencies.id],
      relationName: 'socialLinks',
    }),
  }),
)
export const relations_residencies = relations(residencies, ({ one, many }) => ({
  eventBrand: one(event_brands, {
    fields: [residencies.eventBrand],
    references: [event_brands.id],
    relationName: 'eventBrand',
  }),
  Location_venue: one(venues, {
    fields: [residencies.Location_venue],
    references: [venues.id],
    relationName: 'Location_venue',
  }),
  previewImage: one(media, {
    fields: [residencies.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  socialLinks: many(residencies_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [residencies.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [residencies.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
}))
export const relations_tickets = relations(tickets, ({ one }) => ({
  ticketType: one(ticket_types, {
    fields: [tickets.ticketType],
    references: [ticket_types.id],
    relationName: 'ticketType',
  }),
}))
export const relations_ticket_types_fee = relations(ticket_types_fee, ({ one }) => ({
  _parentID: one(ticket_types, {
    fields: [ticket_types_fee._parentID],
    references: [ticket_types.id],
    relationName: 'fee',
  }),
}))
export const relations_ticket_types = relations(ticket_types, ({ many }) => ({
  fee: many(ticket_types_fee, {
    relationName: 'fee',
  }),
}))
export const relations_users_roles = relations(users_roles, ({ one }) => ({
  parent: one(users, {
    fields: [users_roles.parent],
    references: [users.id],
    relationName: 'roles',
  }),
}))
export const relations_users_tenants_roles = relations(users_tenants_roles, ({ one }) => ({
  parent: one(users_tenants, {
    fields: [users_tenants_roles.parent],
    references: [users_tenants.id],
    relationName: 'roles',
  }),
}))
export const relations_users_tenants = relations(users_tenants, ({ one, many }) => ({
  _parentID: one(users, {
    fields: [users_tenants._parentID],
    references: [users.id],
    relationName: 'tenants',
  }),
  tenant: one(event_organizers, {
    fields: [users_tenants.tenant],
    references: [event_organizers.id],
    relationName: 'tenant',
  }),
  roles: many(users_tenants_roles, {
    relationName: 'roles',
  }),
}))
export const relations_users = relations(users, ({ many }) => ({
  roles: many(users_roles, {
    relationName: 'roles',
  }),
  tenants: many(users_tenants, {
    relationName: 'tenants',
  }),
}))
export const relations_venues_capacities = relations(venues_capacities, ({ one }) => ({
  _parentID: one(venues, {
    fields: [venues_capacities._parentID],
    references: [venues.id],
    relationName: 'capacities',
  }),
}))
export const relations_venues_internal_contacts = relations(
  venues_internal_contacts,
  ({ one }) => ({
    _parentID: one(venues, {
      fields: [venues_internal_contacts._parentID],
      references: [venues.id],
      relationName: 'internalContacts',
    }),
  }),
)
export const relations_venues_social_links = relations(venues_social_links, ({ one }) => ({
  _parentID: one(venues, {
    fields: [venues_social_links._parentID],
    references: [venues.id],
    relationName: 'socialLinks',
  }),
}))
export const relations_venues_texts = relations(venues_texts, ({ one }) => ({
  parent: one(venues, {
    fields: [venues_texts.parent],
    references: [venues.id],
    relationName: '_texts',
  }),
}))
export const relations_venues_rels = relations(venues_rels, ({ one }) => ({
  parent: one(venues, {
    fields: [venues_rels.parent],
    references: [venues.id],
    relationName: '_rels',
  }),
  mediaID: one(media, {
    fields: [venues_rels.mediaID],
    references: [media.id],
    relationName: 'media',
  }),
  documentsID: one(documents, {
    fields: [venues_rels.documentsID],
    references: [documents.id],
    relationName: 'documents',
  }),
}))
export const relations_venues = relations(venues, ({ one, many }) => ({
  hub: one(hubs, {
    fields: [venues.hub],
    references: [hubs.id],
    relationName: 'hub',
  }),
  capacities: many(venues_capacities, {
    relationName: 'capacities',
  }),
  previewImage: one(media, {
    fields: [venues.previewImage],
    references: [media.id],
    relationName: 'previewImage',
  }),
  internalContacts: many(venues_internal_contacts, {
    relationName: 'internalContacts',
  }),
  houseRules: one(documents, {
    fields: [venues.houseRules],
    references: [documents.id],
    relationName: 'houseRules',
  }),
  socialLinks: many(venues_social_links, {
    relationName: 'socialLinks',
  }),
  overview_overview_hero: one(media, {
    fields: [venues.overview_overview_hero],
    references: [media.id],
    relationName: 'overview_overview_hero',
  }),
  meta_meta_image: one(media, {
    fields: [venues.meta_meta_image],
    references: [media.id],
    relationName: 'meta_meta_image',
  }),
  _texts: many(venues_texts, {
    relationName: '_texts',
  }),
  _rels: many(venues_rels, {
    relationName: '_rels',
  }),
}))
export const relations_redirects_rels = relations(redirects_rels, ({ one }) => ({
  parent: one(redirects, {
    fields: [redirects_rels.parent],
    references: [redirects.id],
    relationName: '_rels',
  }),
  pagesID: one(pages, {
    fields: [redirects_rels.pagesID],
    references: [pages.id],
    relationName: 'pages',
  }),
}))
export const relations_redirects = relations(redirects, ({ many }) => ({
  _rels: many(redirects_rels, {
    relationName: '_rels',
  }),
}))
export const relations_search_categories = relations(search_categories, ({ one }) => ({
  _parentID: one(search, {
    fields: [search_categories._parentID],
    references: [search.id],
    relationName: 'categories',
  }),
}))
export const relations_search_rels = relations(search_rels, ({ one }) => ({
  parent: one(search, {
    fields: [search_rels.parent],
    references: [search.id],
    relationName: '_rels',
  }),
  articlesID: one(articles, {
    fields: [search_rels.articlesID],
    references: [articles.id],
    relationName: 'articles',
  }),
}))
export const relations_search = relations(search, ({ one, many }) => ({
  meta_image: one(media, {
    fields: [search.meta_image],
    references: [media.id],
    relationName: 'meta_image',
  }),
  categories: many(search_categories, {
    relationName: 'categories',
  }),
  _rels: many(search_rels, {
    relationName: '_rels',
  }),
}))
export const relations_payload_jobs_log = relations(payload_jobs_log, ({ one }) => ({
  _parentID: one(payload_jobs, {
    fields: [payload_jobs_log._parentID],
    references: [payload_jobs.id],
    relationName: 'log',
  }),
}))
export const relations_payload_jobs = relations(payload_jobs, ({ many }) => ({
  log: many(payload_jobs_log, {
    relationName: 'log',
  }),
}))
export const relations_payload_locked_documents_rels = relations(
  payload_locked_documents_rels,
  ({ one }) => ({
    parent: one(payload_locked_documents, {
      fields: [payload_locked_documents_rels.parent],
      references: [payload_locked_documents.id],
      relationName: '_rels',
    }),
    agenciesID: one(agencies, {
      fields: [payload_locked_documents_rels.agenciesID],
      references: [agencies.id],
      relationName: 'agencies',
    }),
    agentsID: one(agents, {
      fields: [payload_locked_documents_rels.agentsID],
      references: [agents.id],
      relationName: 'agents',
    }),
    articlesID: one(articles, {
      fields: [payload_locked_documents_rels.articlesID],
      references: [articles.id],
      relationName: 'articles',
    }),
    artistDealsID: one(artist_deals, {
      fields: [payload_locked_documents_rels.artistDealsID],
      references: [artist_deals.id],
      relationName: 'artistDeals',
    }),
    artistsID: one(artists, {
      fields: [payload_locked_documents_rels.artistsID],
      references: [artists.id],
      relationName: 'artists',
    }),
    authorsID: one(authors, {
      fields: [payload_locked_documents_rels.authorsID],
      references: [authors.id],
      relationName: 'authors',
    }),
    countriesID: one(countries, {
      fields: [payload_locked_documents_rels.countriesID],
      references: [countries.id],
      relationName: 'countries',
    }),
    documentsID: one(documents, {
      fields: [payload_locked_documents_rels.documentsID],
      references: [documents.id],
      relationName: 'documents',
    }),
    eventBrandsID: one(event_brands, {
      fields: [payload_locked_documents_rels.eventBrandsID],
      references: [event_brands.id],
      relationName: 'eventBrands',
    }),
    eventOrganizersID: one(event_organizers, {
      fields: [payload_locked_documents_rels.eventOrganizersID],
      references: [event_organizers.id],
      relationName: 'eventOrganizers',
    }),
    eventsID: one(events, {
      fields: [payload_locked_documents_rels.eventsID],
      references: [events.id],
      relationName: 'events',
    }),
    fanNotificationListnersID: one(fan_notification_listners, {
      fields: [payload_locked_documents_rels.fanNotificationListnersID],
      references: [fan_notification_listners.id],
      relationName: 'fanNotificationListners',
    }),
    fanUsersID: one(fan_users, {
      fields: [payload_locked_documents_rels.fanUsersID],
      references: [fan_users.id],
      relationName: 'fanUsers',
    }),
    festivalProfilesID: one(festival_profiles, {
      fields: [payload_locked_documents_rels.festivalProfilesID],
      references: [festival_profiles.id],
      relationName: 'festivalProfiles',
    }),
    festivalsID: one(festivals, {
      fields: [payload_locked_documents_rels.festivalsID],
      references: [festivals.id],
      relationName: 'festivals',
    }),
    genresID: one(genres, {
      fields: [payload_locked_documents_rels.genresID],
      references: [genres.id],
      relationName: 'genres',
    }),
    hubsID: one(hubs, {
      fields: [payload_locked_documents_rels.hubsID],
      references: [hubs.id],
      relationName: 'hubs',
    }),
    managersID: one(managers, {
      fields: [payload_locked_documents_rels.managersID],
      references: [managers.id],
      relationName: 'managers',
    }),
    managmentCompaniesID: one(managment_companies, {
      fields: [payload_locked_documents_rels.managmentCompaniesID],
      references: [managment_companies.id],
      relationName: 'managmentCompanies',
    }),
    mediaID: one(media, {
      fields: [payload_locked_documents_rels.mediaID],
      references: [media.id],
      relationName: 'media',
    }),
    ochoEpisodesID: one(ocho_episodes, {
      fields: [payload_locked_documents_rels.ochoEpisodesID],
      references: [ocho_episodes.id],
      relationName: 'ochoEpisodes',
    }),
    ordersID: one(orders, {
      fields: [payload_locked_documents_rels.ordersID],
      references: [orders.id],
      relationName: 'orders',
    }),
    pagesID: one(pages, {
      fields: [payload_locked_documents_rels.pagesID],
      references: [pages.id],
      relationName: 'pages',
    }),
    promotionsID: one(promotions, {
      fields: [payload_locked_documents_rels.promotionsID],
      references: [promotions.id],
      relationName: 'promotions',
    }),
    residenciesID: one(residencies, {
      fields: [payload_locked_documents_rels.residenciesID],
      references: [residencies.id],
      relationName: 'residencies',
    }),
    ticketsID: one(tickets, {
      fields: [payload_locked_documents_rels.ticketsID],
      references: [tickets.id],
      relationName: 'tickets',
    }),
    ticketTypesID: one(ticket_types, {
      fields: [payload_locked_documents_rels.ticketTypesID],
      references: [ticket_types.id],
      relationName: 'ticketTypes',
    }),
    usersID: one(users, {
      fields: [payload_locked_documents_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
    venuesID: one(venues, {
      fields: [payload_locked_documents_rels.venuesID],
      references: [venues.id],
      relationName: 'venues',
    }),
    redirectsID: one(redirects, {
      fields: [payload_locked_documents_rels.redirectsID],
      references: [redirects.id],
      relationName: 'redirects',
    }),
    searchID: one(search, {
      fields: [payload_locked_documents_rels.searchID],
      references: [search.id],
      relationName: 'search',
    }),
    'payload-jobsID': one(payload_jobs, {
      fields: [payload_locked_documents_rels['payload-jobsID']],
      references: [payload_jobs.id],
      relationName: 'payload-jobs',
    }),
  }),
)
export const relations_payload_locked_documents = relations(
  payload_locked_documents,
  ({ many }) => ({
    _rels: many(payload_locked_documents_rels, {
      relationName: '_rels',
    }),
  }),
)
export const relations_payload_preferences_rels = relations(
  payload_preferences_rels,
  ({ one }) => ({
    parent: one(payload_preferences, {
      fields: [payload_preferences_rels.parent],
      references: [payload_preferences.id],
      relationName: '_rels',
    }),
    usersID: one(users, {
      fields: [payload_preferences_rels.usersID],
      references: [users.id],
      relationName: 'users',
    }),
  }),
)
export const relations_payload_preferences = relations(payload_preferences, ({ many }) => ({
  _rels: many(payload_preferences_rels, {
    relationName: '_rels',
  }),
}))
export const relations_payload_migrations = relations(payload_migrations, () => ({}))

type DatabaseSchema = {
  enum_agents_title: typeof enum_agents_title
  enum_articles_type: typeof enum_articles_type
  enum_artist_deals_status_history_status: typeof enum_artist_deals_status_history_status
  enum_artists_social_links_resource: typeof enum_artists_social_links_resource
  enum_authors_social_links_resource: typeof enum_authors_social_links_resource
  enum_event_brands_social_links_resource: typeof enum_event_brands_social_links_resource
  enum_events_external_platform_source_urls_platform: typeof enum_events_external_platform_source_urls_platform
  enum_events_lineup_tier: typeof enum_events_lineup_tier
  enum_events_social_links_resource: typeof enum_events_social_links_resource
  enum_events_marketing_tracking_links_platform: typeof enum_events_marketing_tracking_links_platform
  enum_events_marketing_tracking_links_deals_params: typeof enum_events_marketing_tracking_links_deals_params
  enum_events_origin: typeof enum_events_origin
  enum_events_location_location_type: typeof enum_events_location_location_type
  enum_events_timezone: typeof enum_events_timezone
  enum_fan_notification_listners_type: typeof enum_fan_notification_listners_type
  enum_festival_profiles_social_links_resource: typeof enum_festival_profiles_social_links_resource
  enum_festival_profiles_origin: typeof enum_festival_profiles_origin
  enum_festivals_social_links_resource: typeof enum_festivals_social_links_resource
  enum_festivals_origin: typeof enum_festivals_origin
  enum_genres_type: typeof enum_genres_type
  enum_hubs_type: typeof enum_hubs_type
  enum_orders_origin: typeof enum_orders_origin
  enum_pages_blocks_cta_links_link_type: typeof enum_pages_blocks_cta_links_link_type
  enum_pages_blocks_cta_links_link_appearance: typeof enum_pages_blocks_cta_links_link_appearance
  enum_pages_blocks_content_columns_size: typeof enum_pages_blocks_content_columns_size
  enum_pages_blocks_content_columns_link_type: typeof enum_pages_blocks_content_columns_link_type
  enum_pages_blocks_content_columns_link_appearance: typeof enum_pages_blocks_content_columns_link_appearance
  enum_pages_blocks_archive_populate_by: typeof enum_pages_blocks_archive_populate_by
  enum_pages_blocks_archive_relation_to: typeof enum_pages_blocks_archive_relation_to
  enum_pages_status: typeof enum_pages_status
  enum__pages_v_blocks_cta_links_link_type: typeof enum__pages_v_blocks_cta_links_link_type
  enum__pages_v_blocks_cta_links_link_appearance: typeof enum__pages_v_blocks_cta_links_link_appearance
  enum__pages_v_blocks_content_columns_size: typeof enum__pages_v_blocks_content_columns_size
  enum__pages_v_blocks_content_columns_link_type: typeof enum__pages_v_blocks_content_columns_link_type
  enum__pages_v_blocks_content_columns_link_appearance: typeof enum__pages_v_blocks_content_columns_link_appearance
  enum__pages_v_blocks_archive_populate_by: typeof enum__pages_v_blocks_archive_populate_by
  enum__pages_v_blocks_archive_relation_to: typeof enum__pages_v_blocks_archive_relation_to
  enum__pages_v_version_status: typeof enum__pages_v_version_status
  enum_residencies_social_links_resource: typeof enum_residencies_social_links_resource
  enum_ticket_types_origin: typeof enum_ticket_types_origin
  enum_users_roles: typeof enum_users_roles
  enum_users_tenants_roles: typeof enum_users_tenants_roles
  enum_venues_internal_contacts_type: typeof enum_venues_internal_contacts_type
  enum_venues_social_links_resource: typeof enum_venues_social_links_resource
  enum_venues_origin: typeof enum_venues_origin
  enum_redirects_to_type: typeof enum_redirects_to_type
  enum_payload_jobs_log_task_slug: typeof enum_payload_jobs_log_task_slug
  enum_payload_jobs_log_state: typeof enum_payload_jobs_log_state
  enum_payload_jobs_workflow_slug: typeof enum_payload_jobs_workflow_slug
  enum_payload_jobs_task_slug: typeof enum_payload_jobs_task_slug
  agencies: typeof agencies
  agents: typeof agents
  agents_texts: typeof agents_texts
  articles: typeof articles
  articles_rels: typeof articles_rels
  artist_deals_status_history: typeof artist_deals_status_history
  artist_deals: typeof artist_deals
  artist_deals_rels: typeof artist_deals_rels
  artists_agency_territory_representations: typeof artists_agency_territory_representations
  artists_management_representations: typeof artists_management_representations
  artists_social_links: typeof artists_social_links
  artists: typeof artists
  artists_texts: typeof artists_texts
  artists_rels: typeof artists_rels
  authors_social_links: typeof authors_social_links
  authors: typeof authors
  countries: typeof countries
  documents: typeof documents
  event_brands_social_links: typeof event_brands_social_links
  event_brands: typeof event_brands
  event_organizers: typeof event_organizers
  events_external_platform_source_urls: typeof events_external_platform_source_urls
  events_lineup: typeof events_lineup
  events_social_links: typeof events_social_links
  events_faqs: typeof events_faqs
  events_ticket_types: typeof events_ticket_types
  events_marketing_tracking_links: typeof events_marketing_tracking_links
  events: typeof events
  events_texts: typeof events_texts
  events_rels: typeof events_rels
  fan_notification_listners: typeof fan_notification_listners
  fan_users: typeof fan_users
  fan_users_rels: typeof fan_users_rels
  festival_profiles_social_links: typeof festival_profiles_social_links
  festival_profiles: typeof festival_profiles
  festivals_social_links: typeof festivals_social_links
  festivals: typeof festivals
  genres: typeof genres
  hubs: typeof hubs
  managers: typeof managers
  managment_companies: typeof managment_companies
  media: typeof media
  ocho_episodes: typeof ocho_episodes
  ocho_episodes_rels: typeof ocho_episodes_rels
  orders: typeof orders
  orders_rels: typeof orders_rels
  pages_blocks_cta_links: typeof pages_blocks_cta_links
  pages_blocks_cta: typeof pages_blocks_cta
  pages_blocks_content_columns: typeof pages_blocks_content_columns
  pages_blocks_content: typeof pages_blocks_content
  pages_blocks_media_block: typeof pages_blocks_media_block
  pages_blocks_archive: typeof pages_blocks_archive
  pages_blocks_events_block: typeof pages_blocks_events_block
  pages_blocks_home_events_section: typeof pages_blocks_home_events_section
  pages: typeof pages
  pages_rels: typeof pages_rels
  _pages_v_blocks_cta_links: typeof _pages_v_blocks_cta_links
  _pages_v_blocks_cta: typeof _pages_v_blocks_cta
  _pages_v_blocks_content_columns: typeof _pages_v_blocks_content_columns
  _pages_v_blocks_content: typeof _pages_v_blocks_content
  _pages_v_blocks_media_block: typeof _pages_v_blocks_media_block
  _pages_v_blocks_archive: typeof _pages_v_blocks_archive
  _pages_v_blocks_events_block: typeof _pages_v_blocks_events_block
  _pages_v_blocks_home_events_section: typeof _pages_v_blocks_home_events_section
  _pages_v: typeof _pages_v
  _pages_v_rels: typeof _pages_v_rels
  promotions_code_locks: typeof promotions_code_locks
  promotions: typeof promotions
  residencies_social_links: typeof residencies_social_links
  residencies: typeof residencies
  tickets: typeof tickets
  ticket_types_fee: typeof ticket_types_fee
  ticket_types: typeof ticket_types
  users_roles: typeof users_roles
  users_tenants_roles: typeof users_tenants_roles
  users_tenants: typeof users_tenants
  users: typeof users
  venues_capacities: typeof venues_capacities
  venues_internal_contacts: typeof venues_internal_contacts
  venues_social_links: typeof venues_social_links
  venues: typeof venues
  venues_texts: typeof venues_texts
  venues_rels: typeof venues_rels
  redirects: typeof redirects
  redirects_rels: typeof redirects_rels
  search_categories: typeof search_categories
  search: typeof search
  search_rels: typeof search_rels
  payload_jobs_log: typeof payload_jobs_log
  payload_jobs: typeof payload_jobs
  payload_locked_documents: typeof payload_locked_documents
  payload_locked_documents_rels: typeof payload_locked_documents_rels
  payload_preferences: typeof payload_preferences
  payload_preferences_rels: typeof payload_preferences_rels
  payload_migrations: typeof payload_migrations
  relations_agencies: typeof relations_agencies
  relations_agents_texts: typeof relations_agents_texts
  relations_agents: typeof relations_agents
  relations_articles_rels: typeof relations_articles_rels
  relations_articles: typeof relations_articles
  relations_artist_deals_status_history: typeof relations_artist_deals_status_history
  relations_artist_deals_rels: typeof relations_artist_deals_rels
  relations_artist_deals: typeof relations_artist_deals
  relations_artists_agency_territory_representations: typeof relations_artists_agency_territory_representations
  relations_artists_management_representations: typeof relations_artists_management_representations
  relations_artists_social_links: typeof relations_artists_social_links
  relations_artists_texts: typeof relations_artists_texts
  relations_artists_rels: typeof relations_artists_rels
  relations_artists: typeof relations_artists
  relations_authors_social_links: typeof relations_authors_social_links
  relations_authors: typeof relations_authors
  relations_countries: typeof relations_countries
  relations_documents: typeof relations_documents
  relations_event_brands_social_links: typeof relations_event_brands_social_links
  relations_event_brands: typeof relations_event_brands
  relations_event_organizers: typeof relations_event_organizers
  relations_events_external_platform_source_urls: typeof relations_events_external_platform_source_urls
  relations_events_lineup: typeof relations_events_lineup
  relations_events_social_links: typeof relations_events_social_links
  relations_events_faqs: typeof relations_events_faqs
  relations_events_ticket_types: typeof relations_events_ticket_types
  relations_events_marketing_tracking_links: typeof relations_events_marketing_tracking_links
  relations_events_texts: typeof relations_events_texts
  relations_events_rels: typeof relations_events_rels
  relations_events: typeof relations_events
  relations_fan_notification_listners: typeof relations_fan_notification_listners
  relations_fan_users_rels: typeof relations_fan_users_rels
  relations_fan_users: typeof relations_fan_users
  relations_festival_profiles_social_links: typeof relations_festival_profiles_social_links
  relations_festival_profiles: typeof relations_festival_profiles
  relations_festivals_social_links: typeof relations_festivals_social_links
  relations_festivals: typeof relations_festivals
  relations_genres: typeof relations_genres
  relations_hubs: typeof relations_hubs
  relations_managers: typeof relations_managers
  relations_managment_companies: typeof relations_managment_companies
  relations_media: typeof relations_media
  relations_ocho_episodes_rels: typeof relations_ocho_episodes_rels
  relations_ocho_episodes: typeof relations_ocho_episodes
  relations_orders_rels: typeof relations_orders_rels
  relations_orders: typeof relations_orders
  relations_pages_blocks_cta_links: typeof relations_pages_blocks_cta_links
  relations_pages_blocks_cta: typeof relations_pages_blocks_cta
  relations_pages_blocks_content_columns: typeof relations_pages_blocks_content_columns
  relations_pages_blocks_content: typeof relations_pages_blocks_content
  relations_pages_blocks_media_block: typeof relations_pages_blocks_media_block
  relations_pages_blocks_archive: typeof relations_pages_blocks_archive
  relations_pages_blocks_events_block: typeof relations_pages_blocks_events_block
  relations_pages_blocks_home_events_section: typeof relations_pages_blocks_home_events_section
  relations_pages_rels: typeof relations_pages_rels
  relations_pages: typeof relations_pages
  relations__pages_v_blocks_cta_links: typeof relations__pages_v_blocks_cta_links
  relations__pages_v_blocks_cta: typeof relations__pages_v_blocks_cta
  relations__pages_v_blocks_content_columns: typeof relations__pages_v_blocks_content_columns
  relations__pages_v_blocks_content: typeof relations__pages_v_blocks_content
  relations__pages_v_blocks_media_block: typeof relations__pages_v_blocks_media_block
  relations__pages_v_blocks_archive: typeof relations__pages_v_blocks_archive
  relations__pages_v_blocks_events_block: typeof relations__pages_v_blocks_events_block
  relations__pages_v_blocks_home_events_section: typeof relations__pages_v_blocks_home_events_section
  relations__pages_v_rels: typeof relations__pages_v_rels
  relations__pages_v: typeof relations__pages_v
  relations_promotions_code_locks: typeof relations_promotions_code_locks
  relations_promotions: typeof relations_promotions
  relations_residencies_social_links: typeof relations_residencies_social_links
  relations_residencies: typeof relations_residencies
  relations_tickets: typeof relations_tickets
  relations_ticket_types_fee: typeof relations_ticket_types_fee
  relations_ticket_types: typeof relations_ticket_types
  relations_users_roles: typeof relations_users_roles
  relations_users_tenants_roles: typeof relations_users_tenants_roles
  relations_users_tenants: typeof relations_users_tenants
  relations_users: typeof relations_users
  relations_venues_capacities: typeof relations_venues_capacities
  relations_venues_internal_contacts: typeof relations_venues_internal_contacts
  relations_venues_social_links: typeof relations_venues_social_links
  relations_venues_texts: typeof relations_venues_texts
  relations_venues_rels: typeof relations_venues_rels
  relations_venues: typeof relations_venues
  relations_redirects_rels: typeof relations_redirects_rels
  relations_redirects: typeof relations_redirects
  relations_search_categories: typeof relations_search_categories
  relations_search_rels: typeof relations_search_rels
  relations_search: typeof relations_search
  relations_payload_jobs_log: typeof relations_payload_jobs_log
  relations_payload_jobs: typeof relations_payload_jobs
  relations_payload_locked_documents_rels: typeof relations_payload_locked_documents_rels
  relations_payload_locked_documents: typeof relations_payload_locked_documents
  relations_payload_preferences_rels: typeof relations_payload_preferences_rels
  relations_payload_preferences: typeof relations_payload_preferences
  relations_payload_migrations: typeof relations_payload_migrations
}

declare module '@payloadcms/db-postgres' {
  export interface GeneratedDatabaseSchema {
    schema: DatabaseSchema
  }
}
