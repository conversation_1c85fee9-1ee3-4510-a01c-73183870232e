import type { TaskConfig } from 'payload'
import DicePService from '@/lib/dice-p/service'

export const fetchEventWithTicketTypesTask: TaskConfig<'fetchEventWithTicketTypes'> = {
  slug: 'fetchEventWithTicketTypes',
  retries: 10,
  inputSchema: [
    { name: 'token', type: 'text', required: true },
    { name: 'eventId', type: 'text', required: true },
  ],
  outputSchema: [
    { name: 'ticketTypes', type: 'json', required: true },
  ],
  handler: async ({ input }) => {
    try {
      const token = input.token as string
      const eventId = input.eventId as string
      const dice = new DicePService(token)

      const MAX_ATTEMPTS = 3
      let lastError: unknown

      for (let attempt = 1; attempt <= MAX_ATTEMPTS; attempt++) {
        try {
          const result = await dice.getEventWithTicketTypes({ id: eventId })
          const event = result.viewer?.events?.edges?.[0]?.node
          const ticketTypes = Array.isArray(event?.ticketTypes)
            ? event.ticketTypes
            : []
          return { output: { ticketTypes } }
        } catch {
          if (attempt < MAX_ATTEMPTS) {
            await new Promise((res) => setTimeout(res, attempt * 1000))
          }
        }
      }

    }
    catch {
    }
    return { output: { ticketTypes: [] } }
  },
  label: 'Dice → fetchEventWithTicketTypes (with pre-obtained token)',
}
