import type { Payload } from 'payload'
import type { SendSalesReportInput } from './sendSalesReport'

export class ReportScheduler {
  constructor(private payload: Payload) {}

  async scheduleDailyReports() {
    try {
      const job = await this.payload.jobs.queue({
        task: 'sendSalesReport',
        input: {
          isManual: false,
        } as SendSalesReportInput,
        queue: 'daily-reports',
      })

      this.payload.logger.info(`Daily reports job queued: ${job.id}`)
      return job
    } catch (error) {
      this.payload.logger.error('Error scheduling daily reports:', error)
      throw error
    }
  }

  async scheduleReportForRecipient(recipientId: number, recipientType: 'agent' | 'manager') {
    try {
      const job = await this.payload.jobs.queue({
        task: 'sendSalesReport',
        input: {
          recipientId,
          recipientType,
          isManual: true,
        } as SendSalesReportInput,
        queue: 'manual-reports',
      })

      this.payload.logger.info(
        `Manual report job queued for ${recipientType} ${recipientId}: ${job.id}`,
      )
      return job
    } catch (error) {
      this.payload.logger.error(
        `Error scheduling manual report for ${recipientType} ${recipientId}:`,
        error,
      )
      throw error
    }
  }
}
