import type { TaskConfig } from 'payload'
import { ReportGenerator } from '@/lib/reporting/report-generator'
import { SalesService } from '@/lib/reporting/sales.service'

export interface SendSalesReportInput {
  recipientId?: number
  recipientType?: 'agent' | 'manager'
  isManual?: boolean
}

export const sendSalesReportTask: TaskConfig<'sendSalesReport'> = {
  slug: 'sendSalesReport',
  inputSchema: [
    { name: 'recipientId', type: 'number', required: false },
    {
      name: 'recipientType',
      type: 'select',
      options: [
        { label: 'Agent', value: 'agent' },
        { label: 'Manager', value: 'manager' },
      ],
      required: false,
    },
    { name: 'isManual', type: 'checkbox', required: false },
  ],
  handler: async ({ input, req }): Promise<any> => {
    const payload = req.payload
    const taskInput = input as SendSalesReportInput
    const { recipientId, recipientType, isManual = false } = taskInput

    try {
      const salesService = new SalesService(payload)
      const reportGenerator = new ReportGenerator(payload)

      if (recipientId && recipientType) {
        const result = await reportGenerator.sendReportToRecipient(
          recipientId,
          recipientType,
          isManual,
        )

        payload.logger.info(
          `Sales report sent to ${recipientType} ${recipientId} (${isManual ? 'manual' : 'automated'})`,
        )

        return {
          success: true,
          message: `Report sent to ${recipientType} ${recipientId}`,
          sentCount: result.sent ? 1 : 0,
          errorCount: result.sent ? 0 : 1,
          skippedCount: result.skipped ? 1 : 0,
        }
      } else {
        const recipients = await salesService.getRecipientsWithReporting()
        let sentCount = 0
        let errorCount = 0
        let skippedCount = 0

        for (const recipient of recipients) {
          try {
            const result = await reportGenerator.sendReportToRecipient(
              recipient.recipientId,
              recipient.recipientType,
              isManual,
            )

            if (result.sent) {
              sentCount++
              payload.logger.info(`Daily report sent to ${recipient.recipientEmail}`)
            } else if (result.skipped) {
              skippedCount++
              payload.logger.info(
                `Report skipped for ${recipient.recipientEmail}: ${result.reason}`,
              )
            }
          } catch (error) {
            errorCount++
            payload.logger.error(`Failed to send report to ${recipient.recipientEmail}:`, error)
          }
        }

        payload.logger.info(
          `Daily reports completed: ${sentCount} sent, ${skippedCount} skipped, ${errorCount} errors`,
        )

        return {
          success: true,
          message: `Daily reports: ${sentCount} sent, ${skippedCount} skipped, ${errorCount} errors`,
          sentCount,
          errorCount,
          skippedCount,
        }
      }
    } catch (error) {
      payload.logger.error(`Error in sales report task:`, error)
      throw error
    }
  },
  label: 'Send Sales Report',
}
