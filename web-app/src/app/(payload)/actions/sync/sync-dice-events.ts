import * as cheerio from 'cheerio'
import { PayloadRequest } from 'payload'
import { formatSlug } from '@/fields/slug/formatSlug'
import { Event, Media } from '@/payload-types'
import SpotifyService from '@/lib/spotify/service'
import { getArtistData } from '@/lib/spotify/handle-artist'
import { createLexicalState } from '@/lib/sync/lib'
import { handleImageUpload } from '@/lib/media/lib'
import UserAgent from 'user-agents'
import { indexObject } from '@/lib/sync/indexing'
import { Event as DiceEvent } from '@/lib/dice/schema-types'
export const runtime = 'nodejs'
export const dynamic = 'force-dynamic'

interface Overview {
  overview?: {
    hero?: (number | null) | Media
    content?: {
      root: {
        type: string
        children: {
          type: string
          version: number
        }[]
        direction: ('ltr' | 'rtl') | null
        format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | ''
        indent: number
        version: number
      }
    } | null
  }
}

interface BillingPromoter {
  id: string
  image: {
    url: string
  }
  name: string
  perm_name: string
  profile_active: boolean
}

interface SummaryLineupTopArtist {
  about: string | null
  artist_id: string
  image: {
    url: string
  }
  is_headliner: boolean
  name: string
}

async function handleVenue(venue: any, req: PayloadRequest) {
  venue.streetAddress = venue.address.split(',')[0].trim()
  const upsertVenue = {
    slug: formatSlug(venue.name as string),
    name: venue.name as string,
    address: venue.streetAddress as string,
    city: venue.city.name as string,
    country: venue.city.country_name as string,
    timezone: venue.city.location.place as string,
    coordinates: [venue.location.lng, venue.location.lat],
  }
  const result = await indexObject('venues', upsertVenue, req.payload)
  return result?.id
}

async function handleEventOrganizer(promoter: BillingPromoter, req: PayloadRequest) {
  const { docs } = await req.payload.find({
    collection: 'eventOrganizers',
    where: {
      name: { equals: promoter.name },
    },
  })
  if (docs[0]) return docs[0].id

  let media: Media | undefined
  if (promoter.image?.url) {
    try {
      const fileName = `${promoter.name} `
      media = await handleImageUpload(promoter.image.url, promoter.perm_name, fileName, req.payload)
    } catch (err) {
      console.error(`Failed to upload image for promoter ${promoter.name}:`, err)
    }
  }

  const newEventOrganizer = await req.payload.create({
    collection: 'eventOrganizers',
    data: {
      name: promoter.name,
      slug: formatSlug(promoter.name),
      ...(media && {
        overview: {
          overview: {
            hero: media,
          },
        },
      }),
    },
  })

  return newEventOrganizer.id
}

async function getLineup(eventId: string): Promise<any> {
  const url = `https://api.dice.fm/events/${eventId}/lineup`
  const result = await fetchEventJson(eventId, url)
  const lineup = result.lineup.filter((x: { type: string }) => x.type === 'artist')
  return lineup
}

function deduplicateArtists(artists: SummaryLineupTopArtist[]): SummaryLineupTopArtist[] {
  const seen = new Set<string>()
  return artists.filter((artist) => {
    if (seen.has(artist.artist_id)) return false
    seen.add(artist.artist_id)
    return true
  })
}

async function handleArtists(artists: SummaryLineupTopArtist[], req: PayloadRequest) {
  const uniqueArtists = deduplicateArtists(artists)

  const result = []
  for (const artist of uniqueArtists) {
    const found = await SpotifyService.searchArtists(artist.name)
    if (found.length === 0) continue
    const spotifyArtist = found[0]

    const { docs } = await req.payload.find({
      collection: 'artists',
      where: {
        spotifyId: { equals: spotifyArtist?.id },
      },
    })
    if (docs[0]) {
      result.push(docs[0])
      continue
    }

    if (!spotifyArtist?.id) return
    const { previewImage, socialLinks, genres } = await getArtistData(spotifyArtist.id, req.payload, false)
    const createdArtist = await req.payload.create({
      collection: 'artists',
      data: {
        name: spotifyArtist?.name as string,
        slug: formatSlug(spotifyArtist?.name as string),
        spotifyId: spotifyArtist.id,
        genres,
        socialLinks: socialLinks?.filter((x)=>x.link).map((link) => ({
          resource: link.resource as 'Dice',
          link: link.link,
        })),
        previewImage,
      },
    })
    result.push(createdArtist)
  }
  return result
}



async function fetchEventJson(eventId: string, url: string = ''): Promise<any> {
  if (url.length === 0) url = `https://api.dice.fm/events/${eventId}`
  const userAgent = new UserAgent()
  const res = await fetch(url, { headers: { 'User-Agent': userAgent.toString() } })
  return res.json()
}

async function createEmptyEvent(dice: any, req: PayloadRequest): Promise<any | null> {
  try {
    function toUtcIso(localIsoWithOffset: string): string {
      return new Date(localIsoWithOffset).toISOString()
    }
    const event: Omit<Event, 'sizes' | 'createdAt' | 'updatedAt' | 'id'> = {
      slug: formatSlug(dice.name),
      name: dice.name,
      announcementDate: toUtcIso(dice.dates.announcement_date),
      startDate: toUtcIso(dice.dates.event_start_date),
      endDate: toUtcIso(dice.dates.event_end_date),
      saleOnDate: toUtcIso(dice.dates.sale_start_date),
      saleOffDate: toUtcIso(dice.dates.sale_end_date),
      timezone: dice.dates?.timezone,
      externalDiceId: dice.id,
      lineup: [],
      Location: {
        locationType: 'venue',
      },
    }

    const overview: Overview = { overview: {} }

    if (dice.images) {
      const { square, landscape } = dice.images
      if (square) {
        const fileName = `${dice.name ?? dice.id} square`
        const img = await handleImageUpload(square, formatSlug(dice.name), fileName, req.payload)
        event.previewImage = img.id
      }
      if (landscape) {
        const fileName = `${dice.name ?? dice.id} landscape`
        const img = await handleImageUpload(landscape, formatSlug(dice.name), fileName, req.payload)
        overview.overview!.hero = img.id
      }
    }

    if (dice.about?.description) {
      overview.overview!.content = createLexicalState(dice.about.description)
    }

    event.overview = overview

    if (dice.social_links?.event_share) {
      event.socialLinks = [{ resource: 'Dice', link: dice.social_links.event_share }]
    }

    if (dice.venues?.length > 0) {
      event.Location!.venue = await handleVenue(dice.venues[0], req)
    }

    if (dice.billing_promoter) {
      event.eventOrganizer = await handleEventOrganizer(dice.billing_promoter, req)
    }

    if (dice.summary_lineup && dice.summary_lineup.top_artists) {
      const lineup = await getLineup(dice.id)
      const results = await handleArtists(lineup, req)
      if (results) {
        for (const result of results) {
          event.lineup?.push({
            artist: result.id,
          })
        }
      }
    }
    return event
  } catch (err) {
    console.warn('⚠️ createEmptyEvent error for slug', dice.slug ?? dice.id, err)
    return null
  }
}

export async function importDiceEvent(id: string, request: PayloadRequest, diceEvent: DiceEvent | null = null) {
  try {
    const url = `https://api.dice.fm/events/${id}`
    const userAgent = new UserAgent()
    const res = await fetch(url, { headers: { 'User-Agent': userAgent.toString() } })
    const diceJson = await res.json()
    const event = await createEmptyEvent(diceJson, request) as any

    if (diceEvent) {
      if (diceEvent.url) {
        event.ticketUrls = diceEvent.url ? [diceEvent.url] : []
      }
      if (diceEvent.ticketTypes) {
        event.ticketTypes = diceEvent.ticketTypes;
      }
    }

    const upsertEvent = await indexObject('events', event, request.payload)
    return upsertEvent
  } catch {
    return null
  }
}
