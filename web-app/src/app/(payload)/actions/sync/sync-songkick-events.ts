import type { PayloadRequest } from 'payload'
import { NextResponse } from 'next/server'




export async function syncSongKickEvents(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'syncSongkickWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Songkick concerts' }, { status: 200 })
}

export async function crawlSongKickEvents(req: PayloadRequest) {
  await req.payload.jobs.queue({
    workflow: 'crawlSongkickWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'Synced Songkick concerts' }, { status: 200 })
}

export async function crawlRAEvents(req: PayloadRequest) {
  
  await req.payload.jobs.queue({
    workflow: 'crawlRAWorkflow',
    queue: 'default',
    input: undefined
  })
  return NextResponse.json({ message: 'crawlRAWorkflow' }, { status: 200 })
}
