import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "artists_agency_territory_representations" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"global" boolean DEFAULT false,
  	"agency_id" integer NOT NULL,
  	"verified" boolean DEFAULT true
  );
  
  CREATE TABLE IF NOT EXISTS "artists_management_representations" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL
  );
  
  CREATE TABLE IF NOT EXISTS "artists_texts" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer NOT NULL,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"text" varchar
  );
  
  ALTER TABLE "agencies_texts" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "artists_representation" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "agencies_texts" CASCADE;
  DROP TABLE "artists_representation" CASCADE;
  ALTER TABLE "artists_rels" DROP CONSTRAINT "artists_rels_managment_companies_fk";
  
  DROP INDEX IF EXISTS "artists_rels_managment_companies_id_idx";
  ALTER TABLE "agencies" ADD COLUMN "general_contac_info_email" varchar NOT NULL;
  ALTER TABLE "agencies" ADD COLUMN "general_contac_info_phone_number" varchar NOT NULL;
  ALTER TABLE "artists_rels" ADD COLUMN "countries_id" integer;
  ALTER TABLE "artists_rels" ADD COLUMN "agents_id" integer;
  DO $$ BEGIN
   ALTER TABLE "artists_agency_territory_representations" ADD CONSTRAINT "artists_agency_territory_representations_agency_id_agencies_id_fk" FOREIGN KEY ("agency_id") REFERENCES "public"."agencies"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_agency_territory_representations" ADD CONSTRAINT "artists_agency_territory_representations_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_management_representations" ADD CONSTRAINT "artists_management_representations_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_texts" ADD CONSTRAINT "artists_texts_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "artists_agency_territory_representations_order_idx" ON "artists_agency_territory_representations" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "artists_agency_territory_representations_parent_id_idx" ON "artists_agency_territory_representations" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "artists_agency_territory_representations_agency_idx" ON "artists_agency_territory_representations" USING btree ("agency_id");
  CREATE INDEX IF NOT EXISTS "artists_management_representations_order_idx" ON "artists_management_representations" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "artists_management_representations_parent_id_idx" ON "artists_management_representations" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "artists_texts_order_parent_idx" ON "artists_texts" USING btree ("order","parent_id");
  DO $$ BEGIN
   ALTER TABLE "artists_rels" ADD CONSTRAINT "artists_rels_countries_fk" FOREIGN KEY ("countries_id") REFERENCES "public"."countries"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_rels" ADD CONSTRAINT "artists_rels_agents_fk" FOREIGN KEY ("agents_id") REFERENCES "public"."agents"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "artists_rels_countries_id_idx" ON "artists_rels" USING btree ("countries_id");
  CREATE INDEX IF NOT EXISTS "artists_rels_agents_id_idx" ON "artists_rels" USING btree ("agents_id");
  ALTER TABLE "artists_rels" DROP COLUMN IF EXISTS "managment_companies_id";
  DROP TYPE "public"."enum_artists_representation_coverage";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_artists_representation_coverage" AS ENUM('global', 'continent', 'country');
  CREATE TABLE IF NOT EXISTS "agencies_texts" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer NOT NULL,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"text" varchar
  );
  
  CREATE TABLE IF NOT EXISTS "artists_representation" (
  	"_order" integer NOT NULL,
  	"_parent_id" integer NOT NULL,
  	"id" varchar PRIMARY KEY NOT NULL,
  	"territory_id" integer NOT NULL,
  	"coverage" "enum_artists_representation_coverage" DEFAULT 'global',
  	"agency_id" integer NOT NULL,
  	"agent_id" integer,
  	"management_company_id" integer,
  	"manager_id" integer
  );
  
  ALTER TABLE "artists_agency_territory_representations" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "artists_management_representations" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "artists_texts" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "artists_agency_territory_representations" CASCADE;
  DROP TABLE "artists_management_representations" CASCADE;
  DROP TABLE "artists_texts" CASCADE;
  ALTER TABLE "artists_rels" DROP CONSTRAINT "artists_rels_countries_fk";
  
  ALTER TABLE "artists_rels" DROP CONSTRAINT "artists_rels_agents_fk";
  
  DROP INDEX IF EXISTS "artists_rels_countries_id_idx";
  DROP INDEX IF EXISTS "artists_rels_agents_id_idx";
  ALTER TABLE "artists_rels" ADD COLUMN "managment_companies_id" integer;
  DO $$ BEGIN
   ALTER TABLE "agencies_texts" ADD CONSTRAINT "agencies_texts_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."agencies"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_territory_id_countries_id_fk" FOREIGN KEY ("territory_id") REFERENCES "public"."countries"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_agency_id_agencies_id_fk" FOREIGN KEY ("agency_id") REFERENCES "public"."agencies"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_agent_id_agents_id_fk" FOREIGN KEY ("agent_id") REFERENCES "public"."agents"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_management_company_id_managment_companies_id_fk" FOREIGN KEY ("management_company_id") REFERENCES "public"."managment_companies"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_manager_id_managers_id_fk" FOREIGN KEY ("manager_id") REFERENCES "public"."managers"("id") ON DELETE set null ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "artists_representation" ADD CONSTRAINT "artists_representation_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "agencies_texts_order_parent_idx" ON "agencies_texts" USING btree ("order","parent_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_order_idx" ON "artists_representation" USING btree ("_order");
  CREATE INDEX IF NOT EXISTS "artists_representation_parent_id_idx" ON "artists_representation" USING btree ("_parent_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_territory_idx" ON "artists_representation" USING btree ("territory_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_agency_idx" ON "artists_representation" USING btree ("agency_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_agent_idx" ON "artists_representation" USING btree ("agent_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_management_company_idx" ON "artists_representation" USING btree ("management_company_id");
  CREATE INDEX IF NOT EXISTS "artists_representation_manager_idx" ON "artists_representation" USING btree ("manager_id");
  DO $$ BEGIN
   ALTER TABLE "artists_rels" ADD CONSTRAINT "artists_rels_managment_companies_fk" FOREIGN KEY ("managment_companies_id") REFERENCES "public"."managment_companies"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "artists_rels_managment_companies_id_idx" ON "artists_rels" USING btree ("managment_companies_id");
  ALTER TABLE "agencies" DROP COLUMN IF EXISTS "general_contac_info_email";
  ALTER TABLE "agencies" DROP COLUMN IF EXISTS "general_contac_info_phone_number";
  ALTER TABLE "artists_rels" DROP COLUMN IF EXISTS "countries_id";
  ALTER TABLE "artists_rels" DROP COLUMN IF EXISTS "agents_id";`)
}
