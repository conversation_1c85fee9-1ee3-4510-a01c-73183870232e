import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TABLE IF NOT EXISTS "managers_rels" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"artists_id" integer
  );
  
  DO $$ BEGIN
   ALTER TABLE "managers_rels" ADD CONSTRAINT "managers_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."managers"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "managers_rels" ADD CONSTRAINT "managers_rels_artists_fk" FOREIGN KEY ("artists_id") REFERENCES "public"."artists"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "managers_rels_order_idx" ON "managers_rels" USING btree ("order");
  CREATE INDEX IF NOT EXISTS "managers_rels_parent_idx" ON "managers_rels" USING btree ("parent_id");
  CREATE INDEX IF NOT EXISTS "managers_rels_path_idx" ON "managers_rels" USING btree ("path");
  CREATE INDEX IF NOT EXISTS "managers_rels_artists_id_idx" ON "managers_rels" USING btree ("artists_id");`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   DROP TABLE "managers_rels" CASCADE;`)
}
