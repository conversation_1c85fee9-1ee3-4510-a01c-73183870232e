import { MigrateUpArgs, MigrateDownArgs, sql } from '@payloadcms/db-postgres'

export async function up({ db, payload, req }: MigrateUpArgs): Promise<void> {
  await db.execute(sql`
   CREATE TYPE "public"."enum_agents_title" AS ENUM('partner', 'regular', 'junior', 'coordinator', 'assistant');
  CREATE TABLE IF NOT EXISTS "agencies_texts" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer NOT NULL,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"text" varchar
  );
  
  CREATE TABLE IF NOT EXISTS "agents_texts" (
  	"id" serial PRIMARY KEY NOT NULL,
  	"order" integer NOT NULL,
  	"parent_id" integer NOT NULL,
  	"path" varchar NOT NULL,
  	"text" varchar
  );
  
  ALTER TABLE "agencies" ALTER COLUMN "address" DROP NOT NULL;
  ALTER TABLE "agents"
    ALTER COLUMN "title"
    TYPE enum_agents_title
    USING title::enum_agents_title;
  DO $$ BEGIN
   ALTER TABLE "agencies_texts" ADD CONSTRAINT "agencies_texts_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."agencies"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  DO $$ BEGIN
   ALTER TABLE "agents_texts" ADD CONSTRAINT "agents_texts_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."agents"("id") ON DELETE cascade ON UPDATE no action;
  EXCEPTION
   WHEN duplicate_object THEN null;
  END $$;
  
  CREATE INDEX IF NOT EXISTS "agencies_texts_order_parent_idx" ON "agencies_texts" USING btree ("order","parent_id");
  CREATE INDEX IF NOT EXISTS "agents_texts_order_parent_idx" ON "agents_texts" USING btree ("order","parent_id");
  ALTER TABLE "agencies" DROP COLUMN IF EXISTS "general_contac_info_email";
  ALTER TABLE "agencies" DROP COLUMN IF EXISTS "general_contac_info_phone_number";
  ALTER TABLE "agents" DROP COLUMN IF EXISTS "general_contac_info_email";
  ALTER TABLE "agents" DROP COLUMN IF EXISTS "general_contac_info_phone_number";`)
}

export async function down({ db, payload, req }: MigrateDownArgs): Promise<void> {
  await db.execute(sql`
   ALTER TABLE "agencies_texts" DISABLE ROW LEVEL SECURITY;
  ALTER TABLE "agents_texts" DISABLE ROW LEVEL SECURITY;
  DROP TABLE "agencies_texts" CASCADE;
  DROP TABLE "agents_texts" CASCADE;
  ALTER TABLE "agencies" ALTER COLUMN "address" SET NOT NULL;
  ALTER TABLE "agents" ALTER COLUMN "title" SET DATA TYPE varchar;
  ALTER TABLE "agencies" ADD COLUMN "general_contac_info_email" varchar NOT NULL;
  ALTER TABLE "agencies" ADD COLUMN "general_contac_info_phone_number" varchar NOT NULL;
  ALTER TABLE "agents" ADD COLUMN "general_contac_info_email" varchar NOT NULL;
  ALTER TABLE "agents" ADD COLUMN "general_contac_info_phone_number" varchar NOT NULL;
  DROP TYPE "public"."enum_agents_title";`)
}
