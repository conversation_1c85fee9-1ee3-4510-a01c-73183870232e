import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionAfterChangeHook, CollectionConfig } from 'payload'

const agentAgencyChangeHook: CollectionAfterChangeHook = async ({ previousDoc, doc, req }) => {
  if (!previousDoc || previousDoc.agency === doc.agency) return

  const { docs: artists } = await req.payload.find({
    collection: 'artists',
    where: {
      'agencyTerritoryRepresentations.agents': {
        equals: doc.id,
      },
    },
    depth: 0,
  })

  for (const artist of artists) {
    const updatedBlocks = (artist.agencyTerritoryRepresentations ?? []).map((block) => {
      if (Array.isArray(block.agents) && block.agents.includes(doc.id)) {
        return { ...block, verified: false }
      }
      return block
    })

    await req.payload.update({
      collection: 'artists',
      id: artist.id,
      data: { agencyTerritoryRepresentations: updatedBlocks },
      depth: 0,
    })
  }
}

export const Agents: CollectionConfig = {
  slug: 'agents',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [agentAgencyChangeHook],
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'title',
      type: 'select',
      options: [
        { label: 'Partner / Senior Agent', value: 'partner' },
        { label: 'Regular Agent', value: 'regular' },
        { label: 'Junior Agent', value: 'junior' },
        { label: 'Coordinator', value: 'coordinator' },
        { label: 'Assistant', value: 'assistant' },
      ],
      defaultValue: 'regular',
      required: true,
    },
    {
      name: 'generalContacInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'text',
          required: true,
          hasMany: true,
        },
        {
          name: 'phoneNumber',
          type: 'text',
          required: true,
          hasMany: true,
        },
      ],
    },
    {
      name: 'agency',
      type: 'relationship',
      relationTo: 'agencies',
      required: true,
    },
    {
      name: 'automatedReportingEnabled',
      type: 'checkbox',
      defaultValue: false,
    },
  ],
}
