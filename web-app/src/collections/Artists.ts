import { anyone } from '@/access/anyone'
import { socialLinks } from '@/fields/socialLinks'
import { withSlug } from '@/utilities/_withSlug'
import { metaTab } from '@/fields/metaTab'
import { overviewTab } from '@/fields/overviewTab'
import { getArtist } from '@/app/(payload)/actions/event/get-artist'
import { contributor } from '@/access/contributor'
import { syncLockAfterChangeHook } from '@/hooks/syncLockAfterChangeHook'
import { SyncLockField } from '@/fields/syncLockField'

export const Artists = withSlug({
  slugSource: 'name',
  slug: 'artists',
  access: {
    create: contributor,
    delete: contributor,
    read: anyone,
    update: contributor,
  },
  endpoints: [
    {
      path: '/getArtist',
      method: 'get',
      handler: getArtist,
    },
  ],
  admin: {
    useAsTitle: 'name',
  },
  hooks: {
    afterChange: [syncLockAfterChangeHook],
  },
  fields: [
    SyncLockField,
    {
      name: 'search',
      type: 'text',
      virtual: true,
      admin: {
        position: 'sidebar',
        components: {
          Field: '@/components/SpotifyArtistSelect.tsx',
        },
      },
    },
    {
      type: 'tabs',
      tabs: [
        {
          label: 'General',
          fields: [
            {
              name: 'name',
              type: 'text',
              required: true,
            },
            {
              name: 'spotifyId',
              type: 'text',
              hidden: true,
              required: true,
            },
            {
              name: 'chartMetricExternalId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'externalSanityId',
              type: 'text',
              hidden: true,
            },
            {
              name: 'country',
              type: 'relationship',
              relationTo: 'countries',
            },
            {
              name: 'previewImage',
              type: 'upload',
              relationTo: 'media',
              label: 'A square image',
            },
            {
              name: 'genres',
              type: 'relationship',
              relationTo: 'genres',
              hasMany: true,
            },
            {
              name: 'events',
              type: 'join',
              collection: 'events',
              on: 'lineup.artist',
            },
            {
              name: 'residencies',
              type: 'relationship',
              relationTo: 'residencies',
              hasMany: true,
            },
            {
              name: 'agencyTerritoryRepresentations',
              type: 'array',
              label: 'Agency Territory Representations',
              fields: [
                {
                  name: 'global',
                  type: 'checkbox',
                  label: 'Global Representation',
                  defaultValue: false,
                },
                {
                  name: 'countries',
                  type: 'relationship',
                  relationTo: 'countries',
                  hasMany: true,
                  label: 'Country List',
                  admin: {
                    condition: ({ siblingData }) => !siblingData.global,
                  },
                },
                {
                  name: 'agency',
                  type: 'relationship',
                  relationTo: 'agencies',
                  required: true,
                  label: 'Agency',
                },
                {
                  name: 'agents',
                  type: 'relationship',
                  relationTo: 'agents',
                  hasMany: true,
                  label: 'Agent(s)',
                  admin: {
                    condition: ({ siblingData }) => Boolean(siblingData.agency),
                  },
                },
                {
                  name: 'overrideContactInfo',
                  type: 'group',
                  label: 'Override Contact Info',
                  fields: [
                    { name: 'email', type: 'text', hasMany: true, label: 'Override Email(s)' },
                    { name: 'phoneNumber', type: 'text', hasMany: true, label: 'Override Phone(s)' },
                  ],
                },
                {
                  name: 'verified',
                  type: 'checkbox',
                  label: 'Verified',
                  defaultValue: true,
                },
              ],
            },
            {
              name: 'managementRepresentations',
              label: 'Management Representations',
              type: 'array',
              fields: [
                {
                  name: 'manager',
                  type: 'relationship',
                  relationTo: 'managers',
                  hasMany: true,
                  required: true,
                  label: 'Manager(s)',
                },
                {
                  name: 'overrideContactInfo',
                  type: 'group',
                  label: 'Override Contact Info',
                  fields: [
                    { name: 'email', type: 'text', hasMany: true, label: 'Override Email(s)' },
                    { name: 'phoneNumber', type: 'text', hasMany: true, label: 'Override Phone(s)' },
                  ],
                },
              ],
            },
            {
              name: 'socialLinks',
              type: 'array',
              fields: socialLinks,
            },
          ],
        },
        overviewTab,
        metaTab,
      ],
    },
  ],
})
