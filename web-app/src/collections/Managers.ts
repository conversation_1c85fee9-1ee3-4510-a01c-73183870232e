import { authenticated } from '@/access/authenticated'
import { contributor } from '@/access/contributor'
import { CollectionConfig } from 'payload'

export const Managers: CollectionConfig = {
  slug: 'managers',
  access: {
    create: contributor,
    delete: contributor,
    read: authenticated,
    update: contributor,
  },
  admin: {
    useAsTitle: 'name',
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
    },
    {
      name: 'title',
      type: 'text',
      required: true,
    },
    {
      name: 'generalContacInfo',
      type: 'group',
      fields: [
        {
          name: 'email',
          type: 'text',
          required: true,
        },
        {
          name: 'phoneNumber',
          type: 'text',
          required: true,
        },
      ],
    },
    {
      name: 'managmentCompany',
      type: 'relationship',
      relationTo: 'managmentCompanies',
      required: true,
    },
    {
      name: 'automatedReportingEnabled',
      type: 'checkbox',
      defaultValue: false,
    },
    {
      name: 'artists',
      type: 'relationship',
      relationTo: 'artists',
      hasMany: true,
      admin: {
        description: 'All artists managed by this person',
      },
      hooks: {
        afterRead: [
          async ({ originalDoc, req }) => {
            if (!originalDoc) return [];
            const { docs } = await req.payload.find({
              collection: 'artists',
              where: {
                'managementRepresentations.manager': {
                  contains: originalDoc.id,
                },
              },
              depth: 0,
            });
            return docs.map(a => a.id);
          },
        ],
      },
    },
  ],
}
