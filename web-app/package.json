{"name": "template", "version": "1.0.0", "description": "Marketing Website Template with Payload CMS", "license": "PROPRIETARY", "type": "module", "scripts": {"payload": "payload", "payload:migrate": "payload migrate", "payload:migration": "payload migrate:create", "payload:generate": "payload generate:importmap && payload generate:types && payload generate:db-schema", "build": "next build", "postbuild": "next-sitemap --config next-sitemap.config.cjs", "dev": "NODE_OPTIONS='--inspect' next dev", "start": "NODE_OPTIONS='--inspect' next start", "lint": "next lint", "lint:fix": "next lint --fix", "codegen:dice": "graphql-codegen --config ./codegen/dice.ts", "codegen:p-dice": "graphql-codegen --config ./codegen/p-dice.ts", "codegen": "pnpm run codegen:dice && pnpm run codegen:p-dice"}, "dependencies": {"@aws-sdk/client-s3": "^3.815.0", "@google/genai": "^0.13.0", "@payloadcms/db-postgres": "3.37.0", "@payloadcms/email-nodemailer": "^3.37.0", "@payloadcms/live-preview-react": "3.37.0", "@payloadcms/next": "3.37.0", "@payloadcms/plugin-multi-tenant": "3.37.0", "@payloadcms/plugin-nested-docs": "3.37.0", "@payloadcms/plugin-redirects": "3.37.0", "@payloadcms/plugin-search": "3.37.0", "@payloadcms/plugin-seo": "3.37.0", "@payloadcms/richtext-lexical": "3.37.0", "@payloadcms/storage-s3": "3.37.0", "@payloadcms/ui": "3.37.0", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-slot": "^1.2.2", "@react-hook/window-size": "^3.1.1", "@sanity/client": "^7.2.1", "cheerio": "^1.0.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-react": "^8.6.0", "fetch-cookie": "^3.1.0", "framer-motion": "^12.10.4", "geist": "^1.4.2", "graphql-request": "^7.1.2", "graphql-tag": "^2.12.6", "handlebars": "^4.7.8", "jsdom": "^26.1.0", "lucide-react": "^0.508.0", "mysql2": "^3.14.1", "next": "^15.3.2", "next-sitemap": "^4.2.3", "node-fetch": "^3.3.2", "payload": "3.37.0", "payload-admin-bar": "^1.0.7", "pino": "^9.7.0", "pino-elasticsearch": "^8.1.0", "pino-pretty": "^13.0.0", "prism-react-renderer": "^2.4.1", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "7.56.3", "sharp": "0.34.1", "slugify": "^1.6.6", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "timezones.json": "^1.7.2", "tough-cookie": "^5.1.2", "user-agents": "^1.1.540"}, "devDependencies": {"@eslint/eslintrc": "^3.3.1", "@graphql-codegen/cli": "^5.0.6", "@graphql-codegen/typescript": "^4.1.6", "@graphql-codegen/typescript-graphql-request": "^6.2.0", "@graphql-codegen/typescript-operations": "^4.6.1", "@tailwindcss/postcss": "^4.1.5", "@tailwindcss/typography": "^0.5.16", "@types/escape-html": "^1.0.4", "@types/node": "22.15.17", "@types/react": "19.1.3", "@types/react-dom": "19.1.3", "@types/user-agents": "^1.0.4", "copyfiles": "^2.4.1", "eslint": "^9.26.0", "eslint-config-next": "15.3.2", "graphql": "^16.11.0", "postcss": "^8.5.3", "prettier": "^3.5.3", "tailwindcss": "^4.1.5", "typescript": "5.8.3"}, "pnpm": {"overrides": {"react": "19.1.0", "@types/react": "19.1.3", "react-dom": "19.1.0", "@types/react-dom": "^19.1.3"}, "onlyBuiltDependencies": ["esbuild", "sharp", "unrs-resolver"]}, "engines": {"node": ">=22.0.0"}, "packageManager": "pnpm@10.10.0"}