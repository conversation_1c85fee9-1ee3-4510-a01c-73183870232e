import { withPayload } from '@payloadcms/next/withPayload'
import redirects from './redirects.js'

const NEXT_PUBLIC_SERVER_URL =
  process.env.NEXT_PUBLIC_SERVER_URL ||
  'http://localhost:3000' ||
  'https://grayarea-demo.lumexagency.works'

/** @type {import('next').NextConfig} */
const nextConfig = {
  productionBrowserSourceMaps: true,
  images: {
    remotePatterns: [
      ...[NEXT_PUBLIC_SERVER_URL].map((item) => {
        const url = new URL(item)

        return {
          hostname: url.hostname,
          protocol: url.protocol.replace(':', ''),
        }
      }),
    ],
  },
  reactStrictMode: true,
  redirects,
  output: 'standalone',
  experimental: {
    inlineCss: true,
    serverActions: {
      allowedOrigins: ['*'], //TODO: remove this CSRF disablement for production launch
    },
  },
  serverExternalPackages: ['pino', 'pino-pretty'],
  webpack: (config, { webpack }) => {
    config.plugins.push(
      new webpack.IgnorePlugin({
        resourceRegExp: /^pg-native$|^cloudflare:sockets$/,
      }),
    )

    return config
  },
  cacheMaxMemorySize: 0,
}

export default withPayload(nextConfig, { devBundleServerPackages: false })
